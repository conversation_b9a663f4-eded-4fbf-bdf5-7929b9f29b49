import { Character, CharactersResponse, GetCharactersParams, ChatSession } from '@/types/character';
import { env } from '@/lib/env';

class CharacterService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('accessToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  async getCharacters(params: GetCharactersParams = {}): Promise<CharactersResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);
    if (params.tags) searchParams.append('tags', params.tags);
    if (params.storyMode) searchParams.append('storyMode', params.storyMode);

    const url = `${env.API_BASE_URL}/characters${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch characters');
    }

    return await response.json();
  }

  async getCharacterById(id: string): Promise<Character> {
    const response = await fetch(`${env.API_BASE_URL}/characters/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch character');
    }

    return await response.json();
  }

  async initiateChat(characterId: string): Promise<ChatSession> {
    const response = await fetch(`${env.API_BASE_URL}/characters/${characterId}/initiate-chat`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to initiate chat');
    }

    return await response.json();
  }
}

export const characterService = new CharacterService();
