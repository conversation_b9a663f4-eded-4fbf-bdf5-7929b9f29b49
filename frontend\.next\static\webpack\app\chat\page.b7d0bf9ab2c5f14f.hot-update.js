"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/chat/chat-list */ \"(app-pages-browser)/./src/components/chat/chat-list.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./src/components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/character-profile-sidebar */ \"(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const chatId = searchParams.get('id');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"ChatPage.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    // Auto-select chat from URL parameter\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            const loadChatFromUrl = {\n                \"ChatPage.useEffect.loadChatFromUrl\": async ()=>{\n                    if (chatId && isAuthenticated) {\n                        try {\n                            console.log('Loading chat from URL:', chatId);\n                            // Add small delay to ensure ChatList has loaded\n                            await new Promise({\n                                \"ChatPage.useEffect.loadChatFromUrl\": (resolve)=>setTimeout(resolve, 500)\n                            }[\"ChatPage.useEffect.loadChatFromUrl\"]);\n                            // First try to get chat from the chat list\n                            const chatsResponse = await _services_chat__WEBPACK_IMPORTED_MODULE_7__.chatService.getChats({\n                                limit: 50\n                            });\n                            console.log('All chats:', chatsResponse.data);\n                            const existingChat = chatsResponse.data.find({\n                                \"ChatPage.useEffect.loadChatFromUrl.existingChat\": (chat)=>chat.id === chatId\n                            }[\"ChatPage.useEffect.loadChatFromUrl.existingChat\"]);\n                            if (existingChat) {\n                                console.log('Found chat in list:', existingChat);\n                                setSelectedChat(existingChat);\n                            } else {\n                                console.log('Chat not found in list, trying direct API call');\n                                // Fallback: try direct API call (might not exist)\n                                try {\n                                    const chat = await _services_chat__WEBPACK_IMPORTED_MODULE_7__.chatService.getChatById(chatId);\n                                    console.log('Got chat from direct API:', chat);\n                                    setSelectedChat(chat);\n                                } catch (directError) {\n                                    console.error('Direct API call failed:', directError);\n                                    // If chat not found, remove from URL\n                                    const newUrl = new URL(window.location.href);\n                                    newUrl.searchParams.delete('id');\n                                    window.history.replaceState({}, '', newUrl.toString());\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Failed to load chat from URL:', error);\n                            // If chat not found, remove from URL\n                            const newUrl = new URL(window.location.href);\n                            newUrl.searchParams.delete('id');\n                            window.history.replaceState({}, '', newUrl.toString());\n                        }\n                    }\n                }\n            }[\"ChatPage.useEffect.loadChatFromUrl\"];\n            // Add delay before trying to load chat\n            const timer = setTimeout(loadChatFromUrl, 1000);\n            return ({\n                \"ChatPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"ChatPage.useEffect\"];\n        }\n    }[\"ChatPage.useEffect\"], [\n        chatId,\n        isAuthenticated\n    ]);\n    const handleChatSelect = (chat)=>{\n        setSelectedChat(chat);\n        // Update URL with chat ID\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.set('id', chat.id);\n        window.history.pushState({}, '', newUrl.toString());\n    };\n    const handleBackToList = ()=>{\n        setSelectedChat(null);\n        // Remove chat ID from URL\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.delete('id');\n        window.history.pushState({}, '', newUrl.toString());\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect in useEffect\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarInset, {\n                className: \"flex flex-col h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbPage, {\n                                                    children: \"Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 min-h-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 border-r bg-background flex flex-col min-h-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__.ChatList, {\n                                    selectedChatId: selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id,\n                                    onChatSelect: handleChatSelect\n                                }, selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex min-h-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex flex-col min-h-0\",\n                                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__.ChatInterface, {\n                                            chat: selectedChat,\n                                            onBack: handleBackToList\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center max-w-md mx-auto p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-16 h-16 mx-auto mb-6 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold mb-2\",\n                                                        children: \"Welcome to Bestieku Chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-6\",\n                                                        children: \"Select a chat from the sidebar to start messaging with your AI characters, or go back to the dashboard to start a new conversation.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>router.push('/dashboard'),\n                                                        className: \"inline-flex items-center px-4 py-2 bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-lg transition-colors\",\n                                                        children: \"Browse Characters\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 min-h-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__.CharacterProfileSidebar, {\n                                            characterId: selectedChat.characterId,\n                                            messageCount: selectedChat.messageCount,\n                                            isOpen: isProfileOpen,\n                                            onToggle: ()=>setIsProfileOpen(!isProfileOpen)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"HTDOPaDHmHGUBi1nfrlZjHP5v4o=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});