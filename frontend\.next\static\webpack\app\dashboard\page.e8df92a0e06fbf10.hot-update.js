"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_nav_main__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/nav-main */ \"(app-pages-browser)/./src/components/nav-main.tsx\");\n/* harmony import */ var _components_nav_projects__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/nav-projects */ \"(app-pages-browser)/./src/components/nav-projects.tsx\");\n/* harmony import */ var _components_nav_user__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/nav-user */ \"(app-pages-browser)/./src/components/nav-user.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \n\n\n\n\n\n\nconst data = {\n    navMain: [\n        {\n            title: \"Dashboard\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            isActive: true\n        },\n        {\n            title: \"Characters\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            items: [\n                {\n                    title: \"All Characters\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Fantasy\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Sci-Fi\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Romance\",\n                    url: \"#\"\n                }\n            ]\n        },\n        {\n            title: \"My Chats\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            items: [\n                {\n                    title: \"Recent\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Favorites\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Archived\",\n                    url: \"#\"\n                }\n            ]\n        },\n        {\n            title: \"Settings\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            items: [\n                {\n                    title: \"Profile\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Preferences\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Privacy\",\n                    url: \"#\"\n                }\n            ]\n        }\n    ],\n    projects: [\n        {\n            name: \"Favorites\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: \"Chat History\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            name: \"Top Rated\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ]\n};\nfunction AppSidebar(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.Sidebar, {\n        variant: \"inset\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenu, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenuItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenuButton, {\n                            size: \"lg\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/dashboard\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid flex-1 text-left text-sm leading-tight\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate font-semibold text-sidebar-foreground\",\n                                                children: \"Bestieku\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate text-xs text-sidebar-foreground/70\",\n                                                children: \"Character Chat\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_main__WEBPACK_IMPORTED_MODULE_2__.NavMain, {\n                        items: data.navMain\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_projects__WEBPACK_IMPORTED_MODULE_3__.NavProjects, {\n                        projects: data.projects\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_user__WEBPACK_IMPORTED_MODULE_4__.NavUser, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});