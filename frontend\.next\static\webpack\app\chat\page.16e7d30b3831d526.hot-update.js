"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/castle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sword.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _components_nav_user__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/nav-user */ \"(app-pages-browser)/./src/components/nav-user.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \n\n\n\n\nconst data = {\n    navMain: [\n        {\n            title: \"Discover\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            isActive: true,\n            description: \"Explore AI characters\"\n        },\n        {\n            title: \"My Chats\",\n            url: \"/chat\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"Continue conversations\"\n        },\n        {\n            title: \"Favorites\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Saved characters\"\n        },\n        {\n            title: \"Settings\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Account & preferences\"\n        }\n    ],\n    quickActions: [\n        {\n            name: \"Fantasy\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"from-purple-500 to-pink-500\"\n        },\n        {\n            name: \"Romance\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"from-pink-500 to-rose-500\"\n        },\n        {\n            name: \"Adventure\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"from-orange-500 to-red-500\"\n        },\n        {\n            name: \"Sci-Fi\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"from-blue-500 to-cyan-500\"\n        }\n    ]\n};\nfunction AppSidebar(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n        variant: \"inset\",\n        ...props,\n        className: \"border-r-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarHeader, {\n                className: \"border-b-0 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                            size: \"lg\",\n                            asChild: true,\n                            className: \"hover:bg-transparent p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/dashboard\",\n                                className: \"group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-[#2DD4BF] to-[#14B8A6] text-white flex aspect-square size-12 items-center justify-center rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"size-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid flex-1 text-left leading-tight ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg bg-gradient-to-r from-[#2DD4BF] to-[#14B8A6] bg-clip-text text-transparent\",\n                                                children: \"Bestieku\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-muted-foreground font-medium\",\n                                                children: \"AI Character Chat\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarContent, {\n                className: \"px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3 mb-3\",\n                                children: \"Navigation\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            data.navMain.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                                        asChild: true,\n                                        className: \"h-12 rounded-xl transition-all duration-200 hover:bg-gradient-to-r hover:from-[#2DD4BF]/10 hover:to-[#14B8A6]/10 hover:scale-[1.02] \".concat(item.isActive ? 'bg-gradient-to-r from-[#2DD4BF]/20 to-[#14B8A6]/20 border border-[#2DD4BF]/30' : ''),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: item.url,\n                                            className: \"flex items-center gap-3 px-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg \".concat(item.isActive ? 'bg-[#2DD4BF] text-white shadow-md' : 'bg-muted/50 text-muted-foreground group-hover:bg-[#2DD4BF]/20 group-hover:text-[#2DD4BF]', \" transition-all duration-200\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"size-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium \".concat(item.isActive ? 'text-[#2DD4BF]' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                }, item.title, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3\",\n                                children: \"Quick Categories\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: data.quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: action.url,\n                                        className: \"p-3 rounded-xl bg-gradient-to-br \".concat(action.color, \" text-white hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg group flex flex-col items-center\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                className: \"w-5 h-5 mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-medium\",\n                                                children: action.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, action.name, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarFooter, {\n                className: \"p-4 border-t-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_user__WEBPACK_IMPORTED_MODULE_2__.NavUser, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});