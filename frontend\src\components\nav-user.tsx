"use client"

import { useState } from "react"
import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  User,
} from "lucide-react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/contexts/auth-context"
import { AuthModal } from "@/components/auth/auth-modal"
import { ModeToggleSimple } from "@/components/mode-toggle"
import { useTheme } from "next-themes"

export function NavUser() {
  const { isMobile } = useSidebar()
  const { user, isAuthenticated, logout } = useAuth()
  const { setTheme } = useTheme()
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)

  if (!isAuthenticated) {
    return (
      <>
        <div className="space-y-3">
          <Button
            onClick={() => setIsAuthModalOpen(true)}
            className="w-full h-12 bg-gradient-to-r from-[#2DD4BF] to-[#14B8A6] hover:from-[#14B8A6] hover:to-[#0D9488] text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]"
          >
            <User className="mr-2 h-5 w-5" />
            <span className="font-medium">Get Started</span>
          </Button>

          <div className="flex items-center justify-center">
            <ModeToggleSimple />
          </div>
        </div>
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
        />
      </>
    )
  }

  return (
    <>
      <div className="space-y-3">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="w-full p-3 rounded-xl bg-gradient-to-r from-muted/50 to-muted/30 hover:from-[#2DD4BF]/10 hover:to-[#14B8A6]/10 border border-border/50 hover:border-[#2DD4BF]/30 transition-all duration-200 hover:scale-[1.02] group">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10 ring-2 ring-[#2DD4BF]/20 group-hover:ring-[#2DD4BF]/40 transition-all duration-200">
                  <AvatarImage src={user?.image} alt={user?.name} />
                  <AvatarFallback className="bg-gradient-to-br from-[#2DD4BF] to-[#14B8A6] text-white font-semibold">
                    {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 text-left">
                  <div className="font-medium text-sm">{user?.name}</div>
                  <div className="text-xs text-muted-foreground">{user?.email}</div>
                </div>
                <ChevronsUpDown className="size-4 text-muted-foreground group-hover:text-[#2DD4BF] transition-colors" />
              </div>
            </button>
          </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-64 rounded-xl border-border/50 shadow-xl"
              side={isMobile ? "bottom" : "right"}
              align="end"
              sideOffset={8}
            >
              <DropdownMenuLabel className="p-4 font-normal">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12 ring-2 ring-[#2DD4BF]/20">
                    <AvatarImage src={user?.image} alt={user?.name} />
                    <AvatarFallback className="bg-gradient-to-br from-[#2DD4BF] to-[#14B8A6] text-white font-semibold">
                      {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-semibold">{user?.name}</div>
                    <div className="text-sm text-muted-foreground">{user?.email}</div>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  <BadgeCheck />
                  Account
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Bell />
                  Notifications
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem onClick={() => setTheme("light")}>
                  <Palette />
                  Light Mode
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("dark")}>
                  <Palette />
                  Dark Mode
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("system")}>
                  <Palette />
                  System Theme
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout}>
                <LogOut />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

        <div className="flex items-center justify-center">
          <ModeToggleSimple />
        </div>
      </div>
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </>
  )
}
