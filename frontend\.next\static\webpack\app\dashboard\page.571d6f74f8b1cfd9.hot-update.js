"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Page() {\n    _s();\n    const { isAuthenticated, user, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_1__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarInset, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-screen\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg\",\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_1__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_2__.BreadcrumbPage, {\n                                                    children: \"Character Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col gap-4 p-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold mb-2\",\n                                        children: isAuthenticated ? \"Welcome back, \".concat(user === null || user === void 0 ? void 0 : user.name, \"!\") : 'Welcome to Bestieku'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: isAuthenticated ? 'Choose a character to start chatting with AI companions in immersive stories.' : 'Explore our AI characters and sign in to start chatting in immersive stories.'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border rounded-xl p-4 hover:shadow-md transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted/50 aspect-square rounded-lg mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-1\",\n                                                children: [\n                                                    \"Character \",\n                                                    i + 1\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mb-2\",\n                                                children: \"A mysterious character with an interesting story to tell...\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Fantasy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"⭐ 4.8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            !isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 p-6 bg-muted/30 rounded-xl text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Ready to start chatting?\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"Sign in to unlock personalized conversations and save your chat history.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(Page, \"r/38E3umCJQz9nJgL/a0pLRGLAM=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Heart,History,Home,MessageCircle,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_nav_main__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/nav-main */ \"(app-pages-browser)/./src/components/nav-main.tsx\");\n/* harmony import */ var _components_nav_projects__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/nav-projects */ \"(app-pages-browser)/./src/components/nav-projects.tsx\");\n/* harmony import */ var _components_nav_user__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/nav-user */ \"(app-pages-browser)/./src/components/nav-user.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \n\n\n\n\n\n\nconst data = {\n    navMain: [\n        {\n            title: \"Dashboard\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            isActive: true\n        },\n        {\n            title: \"Characters\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            items: [\n                {\n                    title: \"All Characters\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Fantasy\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Sci-Fi\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Romance\",\n                    url: \"#\"\n                }\n            ]\n        },\n        {\n            title: \"My Chats\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            items: [\n                {\n                    title: \"Recent\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Favorites\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Archived\",\n                    url: \"#\"\n                }\n            ]\n        },\n        {\n            title: \"Settings\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            items: [\n                {\n                    title: \"Profile\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Preferences\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Privacy\",\n                    url: \"#\"\n                }\n            ]\n        }\n    ],\n    projects: [\n        {\n            name: \"Favorites\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: \"Chat History\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            name: \"Top Rated\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ]\n};\nfunction AppSidebar(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.Sidebar, {\n        variant: \"inset\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenu, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenuItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenuButton, {\n                            size: \"lg\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/dashboard\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Heart_History_Home_MessageCircle_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid flex-1 text-left text-sm leading-tight\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate font-medium\",\n                                                children: \"Bestieku\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate text-xs\",\n                                                children: \"Character Chat\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_main__WEBPACK_IMPORTED_MODULE_2__.NavMain, {\n                        items: data.navMain\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_projects__WEBPACK_IMPORTED_MODULE_3__.NavProjects, {\n                        projects: data.projects\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_user__WEBPACK_IMPORTED_MODULE_4__.NavUser, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/auth-modal.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/auth-modal.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthModal: () => (/* binding */ AuthModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ AuthModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AuthModal(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('signin');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pendingAuth, setPendingAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { signIn, signUp, verifyOTP } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [signInData, setSignInData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        provider: 'email',\n        email: ''\n    });\n    const [signUpData, setSignUpData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        provider: 'email',\n        name: '',\n        email: '',\n        dateOfBirth: '',\n        gender: '',\n        about: ''\n    });\n    const [otpCode, setOtpCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const resetForm = ()=>{\n        setSignInData({\n            provider: 'email',\n            email: ''\n        });\n        setSignUpData({\n            provider: 'email',\n            name: '',\n            email: '',\n            dateOfBirth: '',\n            gender: '',\n            about: ''\n        });\n        setOtpCode('');\n        setError(null);\n        setPendingAuth(null);\n        setStep('signin');\n    };\n    const handleClose = ()=>{\n        resetForm();\n        onClose();\n    };\n    const handleSignIn = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(null);\n        try {\n            await signIn(signInData);\n            setPendingAuth({\n                email: signInData.email,\n                provider: signInData.provider\n            });\n            setStep('verify-otp');\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Sign in failed');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSignUp = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(null);\n        try {\n            await signUp(signUpData);\n            setPendingAuth({\n                email: signUpData.email,\n                provider: signUpData.provider\n            });\n            setStep('verify-otp');\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Sign up failed');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleVerifyOTP = async (e)=>{\n        e.preventDefault();\n        if (!pendingAuth) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            const verifyData = {\n                provider: pendingAuth.provider,\n                email: pendingAuth.email,\n                code: otpCode\n            };\n            await verifyOTP(verifyData);\n            handleClose();\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'OTP verification failed');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const renderSignInForm = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSignIn,\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"signin-email\",\n                            className: \"block text-sm font-medium mb-2\",\n                            children: \"Email\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"signin-email\",\n                            type: \"email\",\n                            value: signInData.email,\n                            onChange: (e)=>setSignInData({\n                                    ...signInData,\n                                    email: e.target.value\n                                }),\n                            placeholder: \"Enter your email\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 7\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    type: \"submit\",\n                    className: \"w-full\",\n                    disabled: isLoading,\n                    children: isLoading ? 'Sending OTP...' : 'Send OTP'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setStep('signup'),\n                        className: \"text-sm text-blue-600 hover:underline\",\n                        children: \"Don't have an account? Sign up\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n            lineNumber: 111,\n            columnNumber: 5\n        }, this);\n    const renderSignUpForm = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSignUp,\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"signup-name\",\n                            className: \"block text-sm font-medium mb-2\",\n                            children: \"Name *\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"signup-name\",\n                            type: \"text\",\n                            value: signUpData.name,\n                            onChange: (e)=>setSignUpData({\n                                    ...signUpData,\n                                    name: e.target.value\n                                }),\n                            placeholder: \"Enter your full name (min 3 characters)\",\n                            required: true,\n                            minLength: 3\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"signup-email\",\n                            className: \"block text-sm font-medium mb-2\",\n                            children: \"Email *\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"signup-email\",\n                            type: \"email\",\n                            value: signUpData.email,\n                            onChange: (e)=>setSignUpData({\n                                    ...signUpData,\n                                    email: e.target.value\n                                }),\n                            placeholder: \"Enter your email\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"signup-dob\",\n                            className: \"block text-sm font-medium mb-2\",\n                            children: \"Date of Birth\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"signup-dob\",\n                            type: \"date\",\n                            value: signUpData.dateOfBirth,\n                            onChange: (e)=>setSignUpData({\n                                    ...signUpData,\n                                    dateOfBirth: e.target.value\n                                })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"signup-gender\",\n                            className: \"block text-sm font-medium mb-2\",\n                            children: \"Gender\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            id: \"signup-gender\",\n                            value: signUpData.gender,\n                            onChange: (e)=>setSignUpData({\n                                    ...signUpData,\n                                    gender: e.target.value\n                                }),\n                            className: \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select gender\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"male\",\n                                    children: \"Male\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"female\",\n                                    children: \"Female\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"other\",\n                                    children: \"Other\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"signup-about\",\n                            className: \"block text-sm font-medium mb-2\",\n                            children: \"About\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            id: \"signup-about\",\n                            value: signUpData.about,\n                            onChange: (e)=>setSignUpData({\n                                    ...signUpData,\n                                    about: e.target.value\n                                }),\n                            placeholder: \"Tell us about yourself (optional)\",\n                            className: \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 7\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    type: \"submit\",\n                    className: \"w-full\",\n                    disabled: isLoading,\n                    children: isLoading ? 'Sending OTP...' : 'Sign Up'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setStep('signin'),\n                        className: \"text-sm text-blue-600 hover:underline\",\n                        children: \"Already have an account? Sign in\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n            lineNumber: 147,\n            columnNumber: 5\n        }, this);\n    const renderOTPForm = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleVerifyOTP,\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"We've sent a verification code to \",\n                            pendingAuth === null || pendingAuth === void 0 ? void 0 : pendingAuth.email\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"otp-code\",\n                            className: \"block text-sm font-medium mb-2\",\n                            children: \"Verification Code\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                            id: \"otp-code\",\n                            type: \"text\",\n                            value: otpCode,\n                            onChange: (e)=>setOtpCode(e.target.value),\n                            placeholder: \"Enter 6-digit code\",\n                            maxLength: 6,\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 7\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    type: \"submit\",\n                    className: \"w-full\",\n                    disabled: isLoading,\n                    children: isLoading ? 'Verifying...' : 'Verify Code'\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setStep('signin'),\n                        className: \"text-sm text-blue-600 hover:underline\",\n                        children: \"Back to sign in\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n            lineNumber: 241,\n            columnNumber: 5\n        }, this);\n    const getTitle = ()=>{\n        switch(step){\n            case 'signin':\n                return 'Sign In';\n            case 'signup':\n                return 'Sign Up';\n            case 'verify-otp':\n                return 'Verify Email';\n            default:\n                return 'Authentication';\n        }\n    };\n    const renderContent = ()=>{\n        switch(step){\n            case 'signin':\n                return renderSignInForm();\n            case 'signup':\n                return renderSignUpForm();\n            case 'verify-otp':\n                return renderOTPForm();\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                        children: getTitle()\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                renderContent()\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\auth\\\\auth-modal.tsx\",\n        lineNumber: 310,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthModal, \"LpxJCuUo6uXoCm9Yz/sB+oKPCRE=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = AuthModal;\nvar _c;\n$RefreshReg$(_c, \"AuthModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/auth-modal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/nav-user.tsx":
/*!*************************************!*\
  !*** ./src/components/nav-user.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavUser: () => (/* binding */ NavUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevrons-up-down.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/badge-check.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/auth-modal */ \"(app-pages-browser)/./src/components/auth/auth-modal.tsx\");\n/* harmony import */ var _components_mode_toggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/mode-toggle */ \"(app-pages-browser)/./src/components/mode-toggle.tsx\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ NavUser auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NavUser() {\n    var _user_name_charAt, _user_name, _user_name_charAt1, _user_name1;\n    _s();\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.useSidebar)();\n    const { user, isAuthenticated, logout } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_9__.useTheme)();\n    const [isAuthModalOpen, setIsAuthModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenu, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>setIsAuthModalOpen(true),\n                                    variant: \"outline\",\n                                    className: \"flex-1 justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Sign In / Sign Up\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mode_toggle__WEBPACK_IMPORTED_MODULE_8__.ModeToggleSimple, {}, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__.AuthModal, {\n                    isOpen: isAuthModalOpen,\n                    onClose: ()=>setIsAuthModalOpen(false)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenu, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                    size: \"lg\",\n                                    className: \"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                            className: \"h-8 w-8 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                    src: user === null || user === void 0 ? void 0 : user.image,\n                                                    alt: user === null || user === void 0 ? void 0 : user.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                    className: \"rounded-lg\",\n                                                    children: (user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : (_user_name_charAt = _user_name.charAt(0)) === null || _user_name_charAt === void 0 ? void 0 : _user_name_charAt.toUpperCase()) || 'U'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid flex-1 text-left text-sm leading-tight\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate font-medium\",\n                                                    children: user === null || user === void 0 ? void 0 : user.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate text-xs\",\n                                                    children: user === null || user === void 0 ? void 0 : user.email\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"ml-auto size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                className: \"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\",\n                                side: isMobile ? \"bottom\" : \"right\",\n                                align: \"end\",\n                                sideOffset: 4,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuLabel, {\n                                        className: \"p-0 font-normal\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 px-1 py-1.5 text-left text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                                    className: \"h-8 w-8 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                            src: user === null || user === void 0 ? void 0 : user.image,\n                                                            alt: user === null || user === void 0 ? void 0 : user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                            className: \"rounded-lg\",\n                                                            children: (user === null || user === void 0 ? void 0 : (_user_name1 = user.name) === null || _user_name1 === void 0 ? void 0 : (_user_name_charAt1 = _user_name1.charAt(0)) === null || _user_name_charAt1 === void 0 ? void 0 : _user_name_charAt1.toUpperCase()) || 'U'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid flex-1 text-left text-sm leading-tight\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate font-medium\",\n                                                            children: user === null || user === void 0 ? void 0 : user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate text-xs\",\n                                                            children: user === null || user === void 0 ? void 0 : user.email\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuGroup, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Account\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Notifications\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuGroup, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>setTheme(\"light\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Light Mode\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>setTheme(\"dark\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Dark Mode\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>setTheme(\"system\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"System Theme\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: logout,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Log out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__.AuthModal, {\n                isOpen: isAuthModalOpen,\n                onClose: ()=>setIsAuthModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NavUser, \"ZDpPwI9gg0Ll3Vs0HDhPcwJKKC8=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.useSidebar,\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_themes__WEBPACK_IMPORTED_MODULE_9__.useTheme\n    ];\n});\n_c = NavUser;\nvar _c;\n$RefreshReg$(_c, \"NavUser\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nav-user.tsx\n"));

/***/ })

});