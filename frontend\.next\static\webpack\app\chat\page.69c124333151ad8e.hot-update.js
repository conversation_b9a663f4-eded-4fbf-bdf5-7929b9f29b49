"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-list.tsx":
/*!*******************************************!*\
  !*** ./src/components/chat/chat-list.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatList: () => (/* binding */ ChatList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* __next_internal_client_entry_do_not_use__ ChatList auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatList(param) {\n    let { selectedChatId, onChatSelect } = param;\n    _s();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatList.useEffect\": ()=>{\n            loadChats();\n        }\n    }[\"ChatList.useEffect\"], []);\n    const loadChats = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChats({\n                limit: 50\n            });\n            // Enrich chats with character data\n            const enrichedChats = await Promise.all(response.data.map(async (chat)=>{\n                try {\n                    const character = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterById(chat.characterId);\n                    return {\n                        ...chat,\n                        character: {\n                            id: character.id,\n                            name: character.name,\n                            image: character.image\n                        }\n                    };\n                } catch (error) {\n                    console.error(\"Failed to load character \".concat(chat.characterId, \":\"), error);\n                    return {\n                        ...chat,\n                        character: {\n                            id: chat.characterId,\n                            name: 'Unknown Character',\n                            image: ''\n                        }\n                    };\n                }\n            }));\n            setChats(enrichedChats);\n        } catch (error) {\n            console.error('Failed to load chats:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredChats = chats.filter((chat)=>{\n        var _chat_character, _chat_latestMessage;\n        return ((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_chat_latestMessage = chat.latestMessage) === null || _chat_latestMessage === void 0 ? void 0 : _chat_latestMessage.content.toLowerCase().includes(searchTerm.toLowerCase()));\n    });\n    const formatMessagePreview = function(content) {\n        let maxLength = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        if (content.length <= maxLength) return content;\n        return content.substring(0, maxLength) + '...';\n    };\n    const formatTime = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true\n            });\n        } catch (e) {\n            return '';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-80 border-r bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-10 bg-muted animate-pulse rounded\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 p-2\",\n                    children: Array.from({\n                        length: 5\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-muted rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted rounded w-3/4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted rounded w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b bg-background/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"Chats\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: filteredChats.length\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: \"Search chats...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10 rounded-xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: filteredChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8 text-center text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: searchTerm ? 'No chats found' : 'No chats yet'\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-1\",\n                            children: searchTerm ? 'Try a different search term' : 'Start a conversation with a character'\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1 p-2\",\n                    children: filteredChats.map((chat)=>{\n                        var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2, _chat_character3;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>onChatSelect(chat),\n                            className: \"p-3 rounded-lg cursor-pointer transition-colors hover:bg-muted/50 \".concat(selectedChatId === chat.id ? 'bg-[#2DD4BF]/10 border border-[#2DD4BF]/20' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                        className: \"w-12 h-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                                alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-sm truncate\",\n                                                        children: ((_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name) || 'Unknown Character'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    chat.latestMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: formatTime(chat.latestMessage.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 21\n                                            }, this),\n                                            chat.latestMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground truncate\",\n                                                children: [\n                                                    chat.latestMessage.role === 'user' ? 'You: ' : '',\n                                                    formatMessagePreview(chat.latestMessage.content)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground italic\",\n                                                children: \"No messages yet\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 23\n                                            }, this),\n                                            chat.messageCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        chat.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 17\n                            }, this)\n                        }, chat.id, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-list.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatList, \"ihV39DNjJpDnXV0Dw7kSw4rXTDo=\");\n_c = ChatList;\nvar _c;\n$RefreshReg$(_c, \"ChatList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-list.tsx\n"));

/***/ })

});