"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/services/chat.ts":
/*!******************************!*\
  !*** ./src/services/chat.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chatService: () => (/* binding */ chatService)\n/* harmony export */ });\n/* harmony import */ var _lib_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/env */ \"(app-pages-browser)/./src/lib/env.ts\");\n\nclass ChatService {\n    getAuthHeaders() {\n        const token = localStorage.getItem('accessToken');\n        return {\n            'Content-Type': 'application/json',\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        };\n    }\n    async getChats() {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const searchParams = new URLSearchParams();\n        if (params.page) searchParams.append('page', params.page.toString());\n        if (params.limit) searchParams.append('limit', params.limit.toString());\n        if (params.search) searchParams.append('search', params.search);\n        const url = \"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats\").concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || 'Failed to fetch chats');\n        }\n        return await response.json();\n    }\n    async getChatMessages(chatId) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const searchParams = new URLSearchParams();\n        if (params.page) searchParams.append('page', params.page.toString());\n        if (params.limit) searchParams.append('limit', params.limit.toString());\n        if (params.search) searchParams.append('search', params.search);\n        const url = \"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId, \"/messages\").concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || 'Failed to fetch messages');\n        }\n        return await response.json();\n    }\n    async sendMessage(chatId, data) {\n        console.log('Sending message to API:', \"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId), data);\n        const response = await fetch(\"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId), {\n            method: 'POST',\n            headers: this.getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        console.log('Send message response status:', response.status);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Send message error response:', errorText);\n            try {\n                const error = JSON.parse(errorText);\n                throw new Error(error.message || 'Failed to send message');\n            } catch (e) {\n                throw new Error(\"Failed to send message: \".concat(response.status, \" \").concat(response.statusText));\n            }\n        }\n        const result = await response.json();\n        console.log('Send message success response:', result);\n        return result;\n    }\n    // Create SSE connection for streaming chat using fetch\n    async createStreamConnection(chatId, onMessage, onError) {\n        try {\n            const response = await fetch(\"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId, \"/stream\"), {\n                method: 'GET',\n                headers: {\n                    ...this.getAuthHeaders(),\n                    'Accept': 'text/event-stream',\n                    'Cache-Control': 'no-cache'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to establish stream connection');\n            }\n            if (!response.body) {\n                throw new Error('No response body for stream');\n            }\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            const readStream = async ()=>{\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) break;\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        const lines = chunk.split('\\n');\n                        for (const line of lines){\n                            if (line.startsWith('data: ')) {\n                                const data = line.slice(6);\n                                if (data.trim() && data !== '[DONE]') {\n                                    try {\n                                        const parsed = JSON.parse(data);\n                                        onMessage(parsed);\n                                    } catch (e) {\n                                        console.error('Error parsing SSE data:', e);\n                                    }\n                                }\n                            }\n                        }\n                    }\n                } catch (error) {\n                    onError(error);\n                }\n            };\n            readStream();\n            return {\n                close: ()=>{\n                    reader.cancel();\n                }\n            };\n        } catch (error) {\n            onError(error);\n            return {\n                close: ()=>{}\n            };\n        }\n    }\n    // Alternative method using fetch for SSE with proper headers\n    async createStreamConnectionWithFetch(chatId) {\n        const response = await fetch(\"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId, \"/stream\"), {\n            method: 'GET',\n            headers: {\n                ...this.getAuthHeaders(),\n                'Accept': 'text/event-stream',\n                'Cache-Control': 'no-cache'\n            }\n        });\n        if (!response.ok) {\n            throw new Error('Failed to establish stream connection');\n        }\n        if (!response.body) {\n            throw new Error('No response body for stream');\n        }\n        return response.body;\n    }\n}\nconst chatService = new ChatService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/chat.ts\n"));

/***/ })

});