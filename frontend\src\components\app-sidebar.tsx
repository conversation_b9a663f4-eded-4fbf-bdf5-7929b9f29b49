"use client"

import * as React from "react"
import {
  Bot,
  Heart,
  History,
  Home,
  MessageCircle,
  Settings,
  Star,
  Users,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: Home,
      isActive: true,
    },
    {
      title: "Characters",
      url: "#",
      icon: Bot,
      items: [
        {
          title: "All Characters",
          url: "#",
        },
        {
          title: "Fantasy",
          url: "#",
        },
        {
          title: "Sci-Fi",
          url: "#",
        },
        {
          title: "Romance",
          url: "#",
        },
      ],
    },
    {
      title: "My Chats",
      url: "#",
      icon: MessageCircle,
      items: [
        {
          title: "Recent",
          url: "#",
        },
        {
          title: "Favorites",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Settings",
      url: "#",
      icon: Settings,
      items: [
        {
          title: "Profile",
          url: "#",
        },
        {
          title: "Preferences",
          url: "#",
        },
        {
          title: "Privacy",
          url: "#",
        },
      ],
    },
  ],
  projects: [
    {
      name: "Favorites",
      url: "#",
      icon: Heart,
    },
    {
      name: "Chat History",
      url: "#",
      icon: History,
    },
    {
      name: "Top Rated",
      url: "#",
      icon: Star,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard">
                <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                  <Bot className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">Bestieku</span>
                  <span className="truncate text-xs">Character Chat</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
