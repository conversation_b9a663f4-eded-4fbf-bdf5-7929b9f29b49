'use client';

import { AppSidebar } from "@/components/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { useAuth } from "@/contexts/auth-context"

export default function Page() {
  const { isAuthenticated, user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex items-center justify-center h-screen">
            <div className="text-lg">Loading...</div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    Bestieku
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Character Chat</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="mb-6">
            <h1 className="text-2xl font-bold mb-2">
              {isAuthenticated ? `Welcome back, ${user?.name}!` : 'Welcome to Bestieku'}
            </h1>
            <p className="text-muted-foreground">
              {isAuthenticated
                ? 'Choose a character to start chatting with AI companions in immersive stories.'
                : 'Explore our AI characters and sign in to start chatting in immersive stories.'
              }
            </p>
          </div>

          <div className="grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4">
            {/* Character Cards - Placeholder for now */}
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="bg-card border rounded-xl p-4 hover:shadow-md transition-shadow">
                <div className="bg-muted/50 aspect-square rounded-lg mb-3" />
                <h3 className="font-semibold mb-1">Character {i + 1}</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  A mysterious character with an interesting story to tell...
                </p>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>Fantasy</span>
                  <span>⭐ 4.8</span>
                </div>
              </div>
            ))}
          </div>

          {!isAuthenticated && (
            <div className="mt-8 p-6 bg-muted/30 rounded-xl text-center">
              <h2 className="text-lg font-semibold mb-2">Ready to start chatting?</h2>
              <p className="text-muted-foreground mb-4">
                Sign in to unlock personalized conversations and save your chat history.
              </p>
            </div>
          )}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
