"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-terminal.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/life-buoy.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/frame.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,Command,Frame,LifeBuoy,Map,PieChart,Send,Settings2,SquareTerminal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/command.js\");\n/* harmony import */ var _components_nav_main__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/nav-main */ \"(app-pages-browser)/./src/components/nav-main.tsx\");\n/* harmony import */ var _components_nav_projects__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/nav-projects */ \"(app-pages-browser)/./src/components/nav-projects.tsx\");\n/* harmony import */ var _components_nav_secondary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/nav-secondary */ \"(app-pages-browser)/./src/components/nav-secondary.tsx\");\n/* harmony import */ var _components_nav_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/nav-user */ \"(app-pages-browser)/./src/components/nav-user.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \n\n\n\n\n\n\n\nconst data = {\n    navMain: [\n        {\n            title: \"Playground\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            isActive: true,\n            items: [\n                {\n                    title: \"History\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Starred\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Settings\",\n                    url: \"#\"\n                }\n            ]\n        },\n        {\n            title: \"Models\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            items: [\n                {\n                    title: \"Genesis\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Explorer\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Quantum\",\n                    url: \"#\"\n                }\n            ]\n        },\n        {\n            title: \"Documentation\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            items: [\n                {\n                    title: \"Introduction\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Get Started\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Tutorials\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Changelog\",\n                    url: \"#\"\n                }\n            ]\n        },\n        {\n            title: \"Settings\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            items: [\n                {\n                    title: \"General\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Team\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Billing\",\n                    url: \"#\"\n                },\n                {\n                    title: \"Limits\",\n                    url: \"#\"\n                }\n            ]\n        }\n    ],\n    navSecondary: [\n        {\n            title: \"Support\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            title: \"Feedback\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ],\n    projects: [\n        {\n            name: \"Design Engineering\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            name: \"Sales & Marketing\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            name: \"Travel\",\n            url: \"#\",\n            icon: _barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        }\n    ]\n};\nfunction AppSidebar(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.Sidebar, {\n        variant: \"inset\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenu, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuButton, {\n                            size: \"lg\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Bot_Command_Frame_LifeBuoy_Map_PieChart_Send_Settings2_SquareTerminal_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid flex-1 text-left text-sm leading-tight\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate font-medium\",\n                                                children: \"Acme Inc\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate text-xs\",\n                                                children: \"Enterprise\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_main__WEBPACK_IMPORTED_MODULE_2__.NavMain, {\n                        items: data.navMain\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_projects__WEBPACK_IMPORTED_MODULE_3__.NavProjects, {\n                        projects: data.projects\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_secondary__WEBPACK_IMPORTED_MODULE_4__.NavSecondary, {\n                        items: data.navSecondary,\n                        className: \"mt-auto\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_user__WEBPACK_IMPORTED_MODULE_5__.NavUser, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});