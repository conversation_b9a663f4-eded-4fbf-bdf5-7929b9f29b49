"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/services/chat.ts":
/*!******************************!*\
  !*** ./src/services/chat.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chatService: () => (/* binding */ chatService)\n/* harmony export */ });\n/* harmony import */ var _lib_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/env */ \"(app-pages-browser)/./src/lib/env.ts\");\n\nclass ChatService {\n    getAuthHeaders() {\n        const token = localStorage.getItem('accessToken');\n        return {\n            'Content-Type': 'application/json',\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        };\n    }\n    async getChats() {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const searchParams = new URLSearchParams();\n        if (params.page) searchParams.append('page', params.page.toString());\n        if (params.limit) searchParams.append('limit', params.limit.toString());\n        if (params.search) searchParams.append('search', params.search);\n        const url = \"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats\").concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || 'Failed to fetch chats');\n        }\n        return await response.json();\n    }\n    async getChatById(chatId) {\n        const response = await fetch(\"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId), {\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || 'Failed to fetch chat');\n        }\n        return await response.json();\n    }\n    async getChatMessages(chatId) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const searchParams = new URLSearchParams();\n        if (params.page) searchParams.append('page', params.page.toString());\n        if (params.limit) searchParams.append('limit', params.limit.toString());\n        if (params.search) searchParams.append('search', params.search);\n        const url = \"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId, \"/messages\").concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || 'Failed to fetch messages');\n        }\n        return await response.json();\n    }\n    async sendMessage(chatId, data) {\n        console.log('Sending message to API:', \"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId), data);\n        const response = await fetch(\"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId), {\n            method: 'POST',\n            headers: this.getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        console.log('Send message response status:', response.status);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Send message error response:', errorText);\n            try {\n                const error = JSON.parse(errorText);\n                throw new Error(error.message || 'Failed to send message');\n            } catch (e) {\n                throw new Error(\"Failed to send message: \".concat(response.status, \" \").concat(response.statusText));\n            }\n        }\n        const result = await response.json();\n        console.log('Send message success response:', result);\n        return result;\n    }\n    // Create SSE connection for streaming chat using fetch\n    async createStreamConnection(chatId, onMessage, onError) {\n        console.log('Creating stream connection for chat:', chatId);\n        try {\n            const response = await fetch(\"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId, \"/stream\"), {\n                method: 'GET',\n                headers: {\n                    ...this.getAuthHeaders(),\n                    'Accept': 'text/event-stream',\n                    'Cache-Control': 'no-cache'\n                }\n            });\n            console.log('Stream connection response status:', response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Stream connection error:', errorText);\n                throw new Error(\"Failed to establish stream connection: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error('No response body for stream');\n            }\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let buffer = '';\n            const readStream = async ()=>{\n                try {\n                    while(true){\n                        const { done, value } = await reader.read();\n                        if (done) {\n                            console.log('Stream reading completed');\n                            break;\n                        }\n                        const chunk = decoder.decode(value, {\n                            stream: true\n                        });\n                        buffer += chunk;\n                        // Process complete lines\n                        const lines = buffer.split('\\n');\n                        buffer = lines.pop() || ''; // Keep incomplete line in buffer\n                        for (const line of lines){\n                            if (line.startsWith('data: ')) {\n                                const data = line.slice(6).trim();\n                                console.log('Received SSE data:', data);\n                                if (data && data !== '[DONE]') {\n                                    try {\n                                        const parsed = JSON.parse(data);\n                                        onMessage(parsed);\n                                    } catch (e) {\n                                        console.error('Error parsing SSE data:', e, 'Raw data:', data);\n                                    }\n                                } else if (data === '[DONE]') {\n                                    console.log('Stream completed with [DONE]');\n                                    onMessage({\n                                        event: 'end',\n                                        data: '[DONE]'\n                                    });\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                } catch (error) {\n                    console.error('Stream reading error:', error);\n                    onError(error);\n                }\n            };\n            readStream();\n            return {\n                close: ()=>{\n                    console.log('Closing stream connection');\n                    reader.cancel();\n                }\n            };\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            onError(error);\n            return {\n                close: ()=>{}\n            };\n        }\n    }\n    // Alternative method using fetch for SSE with proper headers\n    async createStreamConnectionWithFetch(chatId) {\n        const response = await fetch(\"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/chats/\").concat(chatId, \"/stream\"), {\n            method: 'GET',\n            headers: {\n                ...this.getAuthHeaders(),\n                'Accept': 'text/event-stream',\n                'Cache-Control': 'no-cache'\n            }\n        });\n        if (!response.ok) {\n            throw new Error('Failed to establish stream connection');\n        }\n        if (!response.body) {\n            throw new Error('No response body for stream');\n        }\n        return response.body;\n    }\n}\nconst chatService = new ChatService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/chat.ts\n"));

/***/ })

});