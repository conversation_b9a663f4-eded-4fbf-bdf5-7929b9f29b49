"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/services/character.ts":
/*!***********************************!*\
  !*** ./src/services/character.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterService: () => (/* binding */ characterService)\n/* harmony export */ });\n/* harmony import */ var _lib_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/env */ \"(app-pages-browser)/./src/lib/env.ts\");\n\nclass CharacterService {\n    getAuthHeaders() {\n        const token = localStorage.getItem('accessToken');\n        return {\n            'Content-Type': 'application/json',\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        };\n    }\n    async getCharacters() {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const searchParams = new URLSearchParams();\n        if (params.page) searchParams.append('page', params.page.toString());\n        if (params.limit) searchParams.append('limit', params.limit.toString());\n        if (params.search) searchParams.append('search', params.search);\n        if (params.tags) searchParams.append('tags', params.tags);\n        if (params.storyMode) searchParams.append('storyMode', params.storyMode);\n        const url = \"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/characters\").concat(searchParams.toString() ? \"?\".concat(searchParams.toString()) : '');\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || 'Failed to fetch characters');\n        }\n        return await response.json();\n    }\n    async getCharacterById(id) {\n        const response = await fetch(\"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/characters/\").concat(id), {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || 'Failed to fetch character');\n        }\n        return await response.json();\n    }\n    async getAllTags() {\n        try {\n            // Fetch first page with large limit to get most characters for tags\n            const response = await this.getCharacters({\n                page: 1,\n                limit: 100\n            });\n            const allTags = response.data.flatMap((character)=>character.tags);\n            const uniqueTags = Array.from(new Set(allTags)).filter((tag)=>tag.trim() !== '');\n            return uniqueTags.sort();\n        } catch (error) {\n            console.error('Failed to fetch tags:', error);\n            return [];\n        }\n    }\n    async initiateChat(characterId) {\n        const response = await fetch(\"\".concat(_lib_env__WEBPACK_IMPORTED_MODULE_0__.env.API_BASE_URL, \"/characters/\").concat(characterId, \"/initiate-chat\"), {\n            method: 'POST',\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || 'Failed to initiate chat');\n        }\n        return await response.json();\n    }\n}\nconst characterService = new CharacterService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/character.ts\n"));

/***/ })

});