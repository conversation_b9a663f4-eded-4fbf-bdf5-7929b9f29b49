"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_character_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/character-card */ \"(app-pages-browser)/./src/components/character-card.tsx\");\n/* harmony import */ var _components_character_search__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/character-search */ \"(app-pages-browser)/./src/components/character-search.tsx\");\n/* harmony import */ var _components_pagination__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pagination */ \"(app-pages-browser)/./src/components/pagination.tsx\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Page() {\n    _s();\n    const { isAuthenticated, user, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [characters, setCharacters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [charactersLoading, setCharactersLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [searchParams, setSearchParams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 12\n    });\n    const [availableTags, setAvailableTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch characters\n    const fetchCharacters = async (params)=>{\n        try {\n            setCharactersLoading(true);\n            const response = await _services_character__WEBPACK_IMPORTED_MODULE_10__.characterService.getCharacters(params);\n            setCharacters(response.data);\n            setTotalPages(response.totalPages);\n            setCurrentPage(response.currentPage);\n            // Extract unique tags from all characters\n            const allTags = response.data.flatMap((character)=>character.tags);\n            const uniqueTags = Array.from(new Set(allTags)).filter((tag)=>tag.trim() !== '');\n            setAvailableTags(uniqueTags);\n        } catch (error) {\n            console.error('Failed to fetch characters:', error);\n        } finally{\n            setCharactersLoading(false);\n        }\n    };\n    // Load characters on mount and when search params change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            fetchCharacters(searchParams);\n        }\n    }[\"Page.useEffect\"], [\n        searchParams\n    ]);\n    // Handle search\n    const handleSearch = (params)=>{\n        const newParams = {\n            ...searchParams,\n            ...params\n        };\n        setSearchParams(newParams);\n    };\n    // Handle page change\n    const handlePageChange = (page)=>{\n        const newParams = {\n            ...searchParams,\n            page\n        };\n        setSearchParams(newParams);\n    };\n    // Handle start chat\n    const handleStartChat = async (characterId)=>{\n        if (!isAuthenticated) {\n            alert('Please sign in to start chatting');\n            return;\n        }\n        try {\n            const chatSession = await _services_character__WEBPACK_IMPORTED_MODULE_10__.characterService.initiateChat(characterId);\n            console.log('Chat initiated:', chatSession);\n            // TODO: Navigate to chat page\n            alert(\"Chat initiated! Session ID: \".concat(chatSession.id));\n        } catch (error) {\n            console.error('Failed to initiate chat:', error);\n            alert('Failed to start chat. Please try again.');\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_2__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-screen\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg\",\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_2__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_4__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbPage, {\n                                                    children: \"Character Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col gap-6 p-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold mb-2\",\n                                        children: isAuthenticated ? \"Welcome back, \".concat(user === null || user === void 0 ? void 0 : user.name, \"!\") : 'Welcome to Bestieku'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: isAuthenticated ? 'Choose a character to start chatting with AI companions in immersive stories.' : 'Explore our AI characters and sign in to start chatting in immersive stories.'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_character_search__WEBPACK_IMPORTED_MODULE_8__.CharacterSearch, {\n                                onSearch: handleSearch,\n                                isLoading: charactersLoading,\n                                availableTags: availableTags\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            charactersLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border rounded-xl p-4 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted/50 aspect-square rounded-lg mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted/50 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted/50 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted/50 rounded w-2/3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this) : characters.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4\",\n                                children: characters.map((character)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_character_card__WEBPACK_IMPORTED_MODULE_7__.CharacterCard, {\n                                        character: character,\n                                        onStartChat: handleStartChat\n                                    }, character.id, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"No characters found matching your criteria.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Try adjusting your search or filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this),\n                            !charactersLoading && characters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pagination__WEBPACK_IMPORTED_MODULE_9__.Pagination, {\n                                currentPage: currentPage,\n                                totalPages: totalPages,\n                                onPageChange: handlePageChange,\n                                isLoading: charactersLoading\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            !isAuthenticated && characters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 p-6 bg-muted/30 rounded-xl text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Ready to start chatting?\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"Sign in to unlock personalized conversations and save your chat history.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(Page, \"0DGgeFxMrTQCNqifc1TvS9IJP5c=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});