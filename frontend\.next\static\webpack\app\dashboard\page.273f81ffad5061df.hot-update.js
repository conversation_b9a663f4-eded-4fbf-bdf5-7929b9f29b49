"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/nav-user.tsx":
/*!*************************************!*\
  !*** ./src/components/nav-user.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavUser: () => (/* binding */ NavUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevrons-up-down.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/badge-check.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BadgeCheck,Bell,ChevronsUpDown,LogOut,Palette,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/auth-modal */ \"(app-pages-browser)/./src/components/auth/auth-modal.tsx\");\n/* harmony import */ var _components_mode_toggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/mode-toggle */ \"(app-pages-browser)/./src/components/mode-toggle.tsx\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ NavUser auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NavUser() {\n    var _user_name_charAt, _user_name, _user_name_charAt1, _user_name1;\n    _s();\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.useSidebar)();\n    const { user, isAuthenticated, logout } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_9__.useTheme)();\n    const [isAuthModalOpen, setIsAuthModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenu, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>setIsAuthModalOpen(true),\n                                    variant: \"outline\",\n                                    className: \"flex-1 justify-start border-sidebar-border hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Sign In / Sign Up\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mode_toggle__WEBPACK_IMPORTED_MODULE_8__.ModeToggleSimple, {}, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__.AuthModal, {\n                    isOpen: isAuthModalOpen,\n                    onClose: ()=>setIsAuthModalOpen(false)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenu, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                    size: \"lg\",\n                                    className: \"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                            className: \"h-8 w-8 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                    src: user === null || user === void 0 ? void 0 : user.image,\n                                                    alt: user === null || user === void 0 ? void 0 : user.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                    className: \"rounded-lg\",\n                                                    children: (user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : (_user_name_charAt = _user_name.charAt(0)) === null || _user_name_charAt === void 0 ? void 0 : _user_name_charAt.toUpperCase()) || 'U'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid flex-1 text-left text-sm leading-tight\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate font-medium\",\n                                                    children: user === null || user === void 0 ? void 0 : user.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate text-xs\",\n                                                    children: user === null || user === void 0 ? void 0 : user.email\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"ml-auto size-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                className: \"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\",\n                                side: isMobile ? \"bottom\" : \"right\",\n                                align: \"end\",\n                                sideOffset: 4,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuLabel, {\n                                        className: \"p-0 font-normal\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 px-1 py-1.5 text-left text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                                    className: \"h-8 w-8 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                            src: user === null || user === void 0 ? void 0 : user.image,\n                                                            alt: user === null || user === void 0 ? void 0 : user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                            className: \"rounded-lg\",\n                                                            children: (user === null || user === void 0 ? void 0 : (_user_name1 = user.name) === null || _user_name1 === void 0 ? void 0 : (_user_name_charAt1 = _user_name1.charAt(0)) === null || _user_name_charAt1 === void 0 ? void 0 : _user_name_charAt1.toUpperCase()) || 'U'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid flex-1 text-left text-sm leading-tight\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate font-medium\",\n                                                            children: user === null || user === void 0 ? void 0 : user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate text-xs\",\n                                                            children: user === null || user === void 0 ? void 0 : user.email\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuGroup, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Account\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Notifications\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuGroup, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>setTheme(\"light\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Light Mode\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>setTheme(\"dark\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Dark Mode\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>setTheme(\"system\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"System Theme\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                        onClick: logout,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BadgeCheck_Bell_ChevronsUpDown_LogOut_Palette_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Log out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__.AuthModal, {\n                isOpen: isAuthModalOpen,\n                onClose: ()=>setIsAuthModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\nav-user.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NavUser, \"ZDpPwI9gg0Ll3Vs0HDhPcwJKKC8=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.useSidebar,\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_themes__WEBPACK_IMPORTED_MODULE_9__.useTheme\n    ];\n});\n_c = NavUser;\nvar _c;\n$RefreshReg$(_c, \"NavUser\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/nav-user.tsx\n"));

/***/ })

});