'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Chat } from '@/types/chat';
import { ChatList } from '@/components/chat/chat-list';
import { ChatInterface } from '@/components/chat/chat-interface';
import { useAuth } from '@/contexts/auth-context';
import { MessageCircle } from 'lucide-react';

export default function ChatPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const searchParams = useSearchParams();
  const router = useRouter();
  const chatId = searchParams.get('id');

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, isLoading, router]);

  const handleChatSelect = (chat: Chat) => {
    setSelectedChat(chat);
    // Update URL with chat ID
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('id', chat.id);
    window.history.pushState({}, '', newUrl.toString());
  };

  const handleBackToList = () => {
    setSelectedChat(null);
    // Remove chat ID from URL
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.delete('id');
    window.history.pushState({}, '', newUrl.toString());
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="h-screen flex bg-background">
      {/* Chat List Sidebar */}
      <ChatList
        selectedChatId={selectedChat?.id}
        onChatSelect={handleChatSelect}
      />

      {/* Chat Interface */}
      <div className="flex-1 flex flex-col">
        {selectedChat ? (
          <ChatInterface
            chat={selectedChat}
            onBack={handleBackToList}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-md mx-auto p-8">
              <MessageCircle className="w-16 h-16 mx-auto mb-6 text-muted-foreground/50" />
              <h2 className="text-xl font-semibold mb-2">Welcome to Bestieku Chat</h2>
              <p className="text-muted-foreground mb-6">
                Select a chat from the sidebar to start messaging with your AI characters, 
                or go back to the dashboard to start a new conversation.
              </p>
              <button
                onClick={() => router.push('/dashboard')}
                className="inline-flex items-center px-4 py-2 bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-lg transition-colors"
              >
                Browse Characters
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
