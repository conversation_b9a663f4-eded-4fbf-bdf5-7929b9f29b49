'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Chat } from '@/types/chat';
import { ChatList } from '@/components/chat/chat-list';
import { ChatInterface } from '@/components/chat/chat-interface';
import { CharacterProfileSidebar } from '@/components/chat/character-profile-sidebar';
import { useAuth } from '@/contexts/auth-context';
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { MessageCircle } from 'lucide-react';

function ChatPageContent() {
  const { isAuthenticated, isLoading } = useAuth();
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [isProfileOpen, setIsProfileOpen] = useState(true);
  const searchParams = useSearchParams();
  const router = useRouter();
  const chatId = searchParams.get('id');

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, isLoading, router]);

  // Handle chats loaded callback
  const handleChatsLoaded = (chats: Chat[]) => {
    console.log('Chats loaded:', chats);

    // Auto-select chat from URL if available
    if (chatId && !selectedChat) {
      const targetChat = chats.find(chat => chat.id === chatId);
      if (targetChat) {
        console.log('Auto-selecting chat from URL:', targetChat);
        setSelectedChat(targetChat);
      } else {
        console.log('Chat not found in loaded chats:', chatId);
        // Remove invalid chat ID from URL
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('id');
        window.history.replaceState({}, '', newUrl.toString());
      }
    }
  };

  const handleChatSelect = (chat: Chat) => {
    setSelectedChat(chat);
    // Update URL with chat ID
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('id', chat.id);
    window.history.pushState({}, '', newUrl.toString());
  };

  const handleBackToList = () => {
    setSelectedChat(null);
    // Remove chat ID from URL
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.delete('id');
    window.history.pushState({}, '', newUrl.toString());
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex flex-col h-screen overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    Bestieku
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Chat</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        {/* Main Chat Area - Fixed height, no scroll */}
        <div className="flex flex-1 min-h-0">
          {/* Chat List Sidebar - Fixed width, own scroll */}
          <div className="w-80 border-r bg-background flex flex-col min-h-0">
            <ChatList
              selectedChatId={selectedChat?.id}
              onChatSelect={handleChatSelect}
              onChatsLoaded={handleChatsLoaded}
            />
          </div>

          {/* Chat Interface - Flexible width, own scroll */}
          <div className="flex-1 flex min-h-0">
            <div className="flex-1 flex flex-col min-h-0">
              {selectedChat ? (
                <ChatInterface
                  chat={selectedChat}
                  onBack={handleBackToList}
                />
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center max-w-md mx-auto p-8">
                    <MessageCircle className="w-16 h-16 mx-auto mb-6 text-muted-foreground/50" />
                    <h2 className="text-xl font-semibold mb-2">Welcome to Bestieku Chat</h2>
                    <p className="text-muted-foreground mb-6">
                      Select a chat from the sidebar to start messaging with your AI characters,
                      or go back to the dashboard to start a new conversation.
                    </p>
                    <button
                      onClick={() => router.push('/dashboard')}
                      className="inline-flex items-center px-4 py-2 bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-lg transition-colors"
                    >
                      Browse Characters
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Character Profile Sidebar - Fixed/flexible width, own scroll */}
            {selectedChat && (
              <div className="flex-shrink-0 min-h-0">
                <CharacterProfileSidebar
                  characterId={selectedChat.characterId}
                  messageCount={selectedChat.messageCount}
                  isOpen={isProfileOpen}
                  onToggle={() => setIsProfileOpen(!isProfileOpen)}
                />
              </div>
            )}
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}

export default function ChatPage() {
  return (
    <Suspense fallback={
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading chat...</p>
        </div>
      </div>
    }>
      <ChatPageContent />
    </Suspense>
  );
}
