'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, X } from 'lucide-react';
import { GetCharactersParams } from '@/types/character';

interface CharacterSearchProps {
  onSearch: (params: GetCharactersParams) => void;
  isLoading?: boolean;
  availableTags?: string[];
}

export function CharacterSearch({ onSearch, isLoading, availableTags = [] }: CharacterSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [storyMode, setStoryMode] = useState<string>('');
  const [showFilters, setShowFilters] = useState(true); // Show by default

  const handleSearch = () => {
    const params: GetCharactersParams = {
      page: 1, // Reset to first page on new search
    };

    if (searchTerm.trim()) {
      params.search = searchTerm.trim();
    }

    if (selectedTags.length > 0) {
      params.tags = selectedTags.join(',');
    }

    if (storyMode) {
      params.storyMode = storyMode;
    }

    onSearch(params);
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedTags([]);
    setStoryMode('');
    onSearch({ page: 1 });
  };

  const hasActiveFilters = searchTerm || selectedTags.length > 0 || storyMode;

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search characters..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            className="pl-10"
          />
        </div>
        <Button
          onClick={() => setShowFilters(!showFilters)}
          variant="outline"
          size="icon"
          className={showFilters ? "bg-[#2DD4BF] text-white hover:bg-[#14B8A6]" : ""}
        >
          <Filter className="w-4 h-4" />
        </Button>
        <Button
          onClick={handleSearch}
          disabled={isLoading}
          className="bg-[#2DD4BF] hover:bg-[#14B8A6] text-white"
        >
          {isLoading ? 'Searching...' : 'Search'}
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-muted/30 rounded-lg p-4 space-y-4">
          {/* Story Mode Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Story Mode</label>
            <div className="flex gap-2">
              <Button
                variant={storyMode === '' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStoryMode('')}
                className={storyMode === '' ? 'bg-[#2DD4BF] hover:bg-[#14B8A6] text-white' : ''}
              >
                All
              </Button>
              <Button
                variant={storyMode === 'true' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStoryMode('true')}
                className={storyMode === 'true' ? 'bg-[#2DD4BF] hover:bg-[#14B8A6] text-white' : ''}
              >
                Story Mode
              </Button>
              <Button
                variant={storyMode === 'false' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStoryMode('false')}
                className={storyMode === 'false' ? 'bg-[#2DD4BF] hover:bg-[#14B8A6] text-white' : ''}
              >
                Regular Chat
              </Button>
            </div>
          </div>

          {/* Tags Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Tags</label>
            {availableTags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {availableTags.map((tag) => (
                  <Badge
                    key={tag}
                    variant={selectedTags.includes(tag) ? 'default' : 'outline'}
                    className={`cursor-pointer ${
                      selectedTags.includes(tag)
                        ? 'bg-[#2DD4BF] hover:bg-[#14B8A6] text-white'
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => handleTagToggle(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No tags available</p>
            )}
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <div className="flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="w-4 h-4 mr-1" />
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {searchTerm && (
            <Badge variant="secondary">
              Search: {searchTerm}
            </Badge>
          )}
          {storyMode && (
            <Badge variant="secondary">
              {storyMode === 'true' ? 'Story Mode' : 'Regular Chat'}
            </Badge>
          )}
          {selectedTags.map((tag) => (
            <Badge key={tag} variant="secondary">
              {tag}
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
