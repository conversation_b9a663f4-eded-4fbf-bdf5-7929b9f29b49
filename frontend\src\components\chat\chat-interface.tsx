'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Chat, Message, SSEEvent } from '@/types/chat';
import { chatService } from '@/services/chat';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Send, MoreVertical } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ChatInterfaceProps {
  chat: Chat;
  onBack?: () => void;
}

export function ChatInterface({ chat, onBack }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [streamingMessage, setStreamingMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const streamConnectionRef = useRef<{ close: () => void } | null>(null);

  useEffect(() => {
    loadMessages();
    return () => {
      // Cleanup stream connection on unmount
      if (streamConnectionRef.current) {
        streamConnectionRef.current.close();
      }
    };
  }, [chat.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, streamingMessage]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await chatService.getChatMessages(chat.id, { limit: 50 });
      setMessages(response.data.reverse()); // Reverse to show oldest first
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    const messageText = newMessage.trim();
    const tempId = `temp-${Date.now()}`;
    setNewMessage('');
    setSending(true);

    try {
      // Add user message to UI immediately
      const userMessage: Message = {
        id: tempId,
        role: 'user',
        content: messageText,
        contentType: 'text',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setMessages(prev => [...prev, userMessage]);

      console.log('Sending message to chat:', chat.id, { message: messageText, streaming: true });

      // Send message with streaming enabled
      const response = await chatService.sendMessage(chat.id, {
        message: messageText,
        streaming: true,
      });

      console.log('Message sent successfully:', response);

      // Update the temporary message with real ID if available
      if (response.id) {
        setMessages(prev => prev.map(msg =>
          msg.id === tempId ? { ...msg, id: response.id } : msg
        ));
      }

      // Start streaming response
      await startStreaming();

    } catch (error) {
      console.error('Failed to send message:', error);
      // Remove the temporary user message on error
      setMessages(prev => prev.filter(msg => msg.id !== tempId));
      alert('Failed to send message. Please try again.');
    } finally {
      setSending(false);
    }
  };

  const startStreaming = async () => {
    console.log('Starting stream for chat:', chat.id);
    setIsStreaming(true);
    setStreamingMessage('');

    // Close existing connection
    if (streamConnectionRef.current) {
      streamConnectionRef.current.close();
    }

    let currentStreamingMessage = '';

    const onMessage = (data: SSEEvent) => {
      console.log('Received SSE event:', data);

      switch (data.event) {
        case 'start':
          console.log('Stream started');
          currentStreamingMessage = '';
          setStreamingMessage('');
          break;
        case 'token':
          currentStreamingMessage += data.data;
          setStreamingMessage(currentStreamingMessage);
          break;
        case 'metadata':
          console.log('Stream metadata:', data.data);
          break;
        case 'end':
          console.log('Stream ended, final message:', currentStreamingMessage);
          // Finalize the streaming message
          const finalMessage: Message = {
            id: data.data?.chatMessageId || `msg-${Date.now()}`,
            role: 'assistant',
            content: currentStreamingMessage,
            contentType: 'text',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          setMessages(prev => [...prev, finalMessage]);
          setStreamingMessage('');
          setIsStreaming(false);
          if (streamConnectionRef.current) {
            streamConnectionRef.current.close();
          }
          break;
        default:
          console.log('Unknown SSE event:', data.event);
      }
    };

    const onError = async (error: any) => {
      console.error('Stream Error:', error);
      setIsStreaming(false);
      setStreamingMessage('');
      if (streamConnectionRef.current) {
        streamConnectionRef.current.close();
      }

      // Fallback: try to reload messages to get the AI response
      console.log('Attempting to reload messages as fallback...');
      try {
        await loadMessages();
      } catch (reloadError) {
        console.error('Failed to reload messages:', reloadError);
        alert('Failed to receive AI response. Please try again.');
      }
    };

    try {
      // Create new stream connection
      const connection = await chatService.createStreamConnection(chat.id, onMessage, onError);
      streamConnectionRef.current = connection;

      // Set timeout for streaming (30 seconds)
      setTimeout(() => {
        if (isStreaming) {
          console.log('Stream timeout, falling back to message reload');
          onError(new Error('Stream timeout'));
        }
      }, 30000);

    } catch (error) {
      console.error('Failed to create stream connection:', error);
      setIsStreaming(false);
      alert('Failed to establish connection for AI response. Please try again.');
    }
  };

  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return '';
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (loading) {
    return (
      <div className="flex-1 flex flex-col">
        <div className="border-b p-4 animate-pulse">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-muted rounded-full" />
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded w-32" />
              <div className="h-3 bg-muted rounded w-20" />
            </div>
          </div>
        </div>
        <div className="flex-1 p-4 space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex space-x-3 animate-pulse">
              <div className="w-8 h-8 bg-muted rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-4 bg-muted rounded w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Chat Header */}
      <div className="border-b p-4 bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="w-10 h-10">
              <AvatarImage src={chat.character?.image} alt={chat.character?.name} />
              <AvatarFallback>
                {chat.character?.name?.charAt(0)?.toUpperCase() || 'C'}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="font-semibold">{chat.character?.name || 'Unknown Character'}</h2>
              <p className="text-sm text-muted-foreground">
                {chat.messageCount} messages
              </p>
            </div>
          </div>
          <Button variant="ghost" size="icon">
            <MoreVertical className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[70%] rounded-lg p-3 ${
                message.role === 'user'
                  ? 'bg-[#2DD4BF] text-white'
                  : 'bg-muted'
              }`}
            >
              <p className="text-sm whitespace-pre-wrap">{message.content}</p>
              <p className={`text-xs mt-1 ${
                message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'
              }`}>
                {formatTime(message.createdAt)}
              </p>
            </div>
          </div>
        ))}

        {/* Streaming Message */}
        {isStreaming && streamingMessage && (
          <div className="flex justify-start">
            <div className="max-w-[70%] rounded-lg p-3 bg-muted">
              <p className="text-sm whitespace-pre-wrap">{streamingMessage}</p>
              <div className="flex items-center mt-1">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="border-t p-4 bg-background">
        <div className="flex space-x-2">
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            disabled={sending || isStreaming}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sending || isStreaming}
            className="bg-[#2DD4BF] hover:bg-[#14B8A6] text-white"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
