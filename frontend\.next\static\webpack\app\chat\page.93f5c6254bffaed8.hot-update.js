"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/chat/chat-list */ \"(app-pages-browser)/./src/components/chat/chat-list.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./src/components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/character-profile-sidebar */ \"(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatPageContent() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const chatId = searchParams.get('id');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                // Show auth modal instead of redirecting\n                setShowAuthModal(true);\n            }\n        }\n    }[\"ChatPageContent.useEffect\"], [\n        isAuthenticated,\n        isLoading\n    ]);\n    // Handle chats loaded callback\n    const handleChatsLoaded = (chats)=>{\n        console.log('Chats loaded:', chats);\n        // Auto-select chat from URL if available\n        if (chatId && !selectedChat) {\n            const targetChat = chats.find((chat)=>chat.id === chatId);\n            if (targetChat) {\n                console.log('Auto-selecting chat from URL:', targetChat);\n                setSelectedChat(targetChat);\n            } else {\n                console.log('Chat not found in loaded chats:', chatId);\n                // Remove invalid chat ID from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('id');\n                window.history.replaceState({}, '', newUrl.toString());\n            }\n        }\n    };\n    const handleChatSelect = (chat)=>{\n        setSelectedChat(chat);\n        // Update URL with chat ID\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.set('id', chat.id);\n        window.history.pushState({}, '', newUrl.toString());\n    };\n    const handleBackToList = ()=>{\n        setSelectedChat(null);\n        // Remove chat ID from URL\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.delete('id');\n        window.history.pushState({}, '', newUrl.toString());\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect in useEffect\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_10__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_7__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_10__.SidebarInset, {\n                className: \"flex flex-col h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_10__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbPage, {\n                                                    children: \"Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 min-h-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 border-r bg-background flex flex-col min-h-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__.ChatList, {\n                                    selectedChatId: selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id,\n                                    onChatSelect: handleChatSelect,\n                                    onChatsLoaded: handleChatsLoaded\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex min-h-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex flex-col min-h-0\",\n                                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__.ChatInterface, {\n                                            chat: selectedChat,\n                                            onBack: handleBackToList\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center max-w-md mx-auto p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-16 h-16 mx-auto mb-6 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold mb-2\",\n                                                        children: \"Welcome to Bestieku Chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-6\",\n                                                        children: \"Select a chat from the sidebar to start messaging with your AI characters, or go back to the dashboard to start a new conversation.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>router.push('/dashboard'),\n                                                        className: \"inline-flex items-center px-4 py-2 bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-lg transition-colors\",\n                                                        children: \"Browse Characters\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 min-h-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__.CharacterProfileSidebar, {\n                                            characterId: selectedChat.characterId,\n                                            messageCount: selectedChat.messageCount,\n                                            isOpen: isProfileOpen,\n                                            onToggle: ()=>setIsProfileOpen(!isProfileOpen)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPageContent, \"5wLwgIU2xxjMwmLGQvAkdgeIGUg=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ChatPageContent;\nfunction ChatPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatPageContent, {}, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ChatPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatPageContent\");\n$RefreshReg$(_c1, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});