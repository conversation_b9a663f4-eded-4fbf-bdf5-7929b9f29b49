"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_character_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/character-card */ \"(app-pages-browser)/./src/components/character-card.tsx\");\n/* harmony import */ var _components_character_search__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/character-search */ \"(app-pages-browser)/./src/components/character-search.tsx\");\n/* harmony import */ var _components_pagination__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pagination */ \"(app-pages-browser)/./src/components/pagination.tsx\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Page() {\n    _s();\n    const { isAuthenticated, user, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [characters, setCharacters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [charactersLoading, setCharactersLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [searchParams, setSearchParams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 12\n    });\n    const [availableTags, setAvailableTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingCharacterId, setPendingCharacterId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch characters\n    const fetchCharacters = async (params)=>{\n        try {\n            setCharactersLoading(true);\n            const response = await _services_character__WEBPACK_IMPORTED_MODULE_11__.characterService.getCharacters(params);\n            setCharacters(response.data);\n            setTotalPages(response.totalPages);\n            setCurrentPage(response.currentPage);\n        } catch (error) {\n            console.error('Failed to fetch characters:', error);\n        } finally{\n            setCharactersLoading(false);\n        }\n    };\n    // Load available tags on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            const loadTags = {\n                \"Page.useEffect.loadTags\": async ()=>{\n                    const tags = await _services_character__WEBPACK_IMPORTED_MODULE_11__.characterService.getAllTags();\n                    setAvailableTags(tags);\n                }\n            }[\"Page.useEffect.loadTags\"];\n            loadTags();\n        }\n    }[\"Page.useEffect\"], []);\n    // Load characters on mount and when search params change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            fetchCharacters(searchParams);\n        }\n    }[\"Page.useEffect\"], [\n        searchParams\n    ]);\n    // Handle search\n    const handleSearch = (params)=>{\n        const newParams = {\n            ...searchParams,\n            ...params\n        };\n        setSearchParams(newParams);\n    };\n    // Handle page change\n    const handlePageChange = (page)=>{\n        const newParams = {\n            ...searchParams,\n            page\n        };\n        setSearchParams(newParams);\n    };\n    // Handle start chat\n    const handleStartChat = async (characterId)=>{\n        if (!isAuthenticated) {\n            alert('Please sign in to start chatting');\n            return;\n        }\n        try {\n            const chatSession = await _services_character__WEBPACK_IMPORTED_MODULE_11__.characterService.initiateChat(characterId);\n            console.log('Chat initiated:', chatSession);\n            // Navigate to chat page with the new chat\n            router.push(\"/chat?id=\".concat(chatSession.id));\n        } catch (error) {\n            console.error('Failed to initiate chat:', error);\n            alert('Failed to start chat. Please try again.');\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_3__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-screen\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg\",\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_3__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbPage, {\n                                                    children: \"Character Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col gap-6 p-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold mb-2\",\n                                        children: isAuthenticated ? \"Welcome back, \".concat(user === null || user === void 0 ? void 0 : user.name, \"!\") : 'Welcome to Bestieku'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: isAuthenticated ? 'Choose a character to start chatting with AI companions in immersive stories.' : 'Explore our AI characters and sign in to start chatting in immersive stories.'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_character_search__WEBPACK_IMPORTED_MODULE_9__.CharacterSearch, {\n                                onSearch: handleSearch,\n                                isLoading: charactersLoading,\n                                availableTags: availableTags\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            charactersLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border rounded-xl p-4 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted/50 aspect-square rounded-lg mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted/50 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted/50 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted/50 rounded w-2/3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this) : characters.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4\",\n                                children: characters.map((character)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_character_card__WEBPACK_IMPORTED_MODULE_8__.CharacterCard, {\n                                        character: character,\n                                        onStartChat: handleStartChat\n                                    }, character.id, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"No characters found matching your criteria.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Try adjusting your search or filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            !charactersLoading && characters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pagination__WEBPACK_IMPORTED_MODULE_10__.Pagination, {\n                                currentPage: currentPage,\n                                totalPages: totalPages,\n                                onPageChange: handlePageChange,\n                                isLoading: charactersLoading\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            !isAuthenticated && characters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 p-6 bg-muted/30 rounded-xl text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Ready to start chatting?\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"Sign in to unlock personalized conversations and save your chat history.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n_s(Page, \"/i5VuppABDqskQNN3RpAMnOKNOU=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDQTtBQUNTO0FBUWxCO0FBQ2tCO0FBS3JCO0FBQ2lCO0FBQ1U7QUFDSTtBQUNYO0FBQ0c7QUFJeEMsU0FBU21COztJQUN0QixNQUFNLEVBQUVDLGVBQWUsRUFBRUMsSUFBSSxFQUFFQyxTQUFTLEVBQUUsR0FBR1IsK0RBQU9BO0lBQ3BELE1BQU1TLFNBQVNyQiwwREFBU0E7SUFDeEIsTUFBTSxDQUFDc0IsWUFBWUMsY0FBYyxHQUFHeEIsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUN5QixtQkFBbUJDLHFCQUFxQixHQUFHMUIsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDMkIsWUFBWUMsY0FBYyxHQUFHNUIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDNkIsYUFBYUMsZUFBZSxHQUFHOUIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDK0IsY0FBY0MsZ0JBQWdCLEdBQUdoQywrQ0FBUUEsQ0FBc0I7UUFBRWlDLE1BQU07UUFBR0MsT0FBTztJQUFHO0lBQzNGLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUdwQywrQ0FBUUEsQ0FBVyxFQUFFO0lBQy9ELE1BQU0sQ0FBQ3FDLGVBQWVDLGlCQUFpQixHQUFHdEMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDdUMsb0JBQW9CQyxzQkFBc0IsR0FBR3hDLCtDQUFRQSxDQUFnQjtJQUU1RSxtQkFBbUI7SUFDbkIsTUFBTXlDLGtCQUFrQixPQUFPQztRQUM3QixJQUFJO1lBQ0ZoQixxQkFBcUI7WUFDckIsTUFBTWlCLFdBQVcsTUFBTTFCLGtFQUFnQkEsQ0FBQzJCLGFBQWEsQ0FBQ0Y7WUFDdERsQixjQUFjbUIsU0FBU0UsSUFBSTtZQUMzQmpCLGNBQWNlLFNBQVNoQixVQUFVO1lBQ2pDRyxlQUFlYSxTQUFTZCxXQUFXO1FBQ3JDLEVBQUUsT0FBT2lCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7UUFDL0MsU0FBVTtZQUNScEIscUJBQXFCO1FBQ3ZCO0lBQ0Y7SUFFQSwrQkFBK0I7SUFDL0IzQixnREFBU0E7MEJBQUM7WUFDUixNQUFNaUQ7MkNBQVc7b0JBQ2YsTUFBTUMsT0FBTyxNQUFNaEMsa0VBQWdCQSxDQUFDaUMsVUFBVTtvQkFDOUNkLGlCQUFpQmE7Z0JBQ25COztZQUNBRDtRQUNGO3lCQUFHLEVBQUU7SUFFTCx5REFBeUQ7SUFDekRqRCxnREFBU0E7MEJBQUM7WUFDUjBDLGdCQUFnQlY7UUFDbEI7eUJBQUc7UUFBQ0E7S0FBYTtJQUVqQixnQkFBZ0I7SUFDaEIsTUFBTW9CLGVBQWUsQ0FBQ1Q7UUFDcEIsTUFBTVUsWUFBWTtZQUFFLEdBQUdyQixZQUFZO1lBQUUsR0FBR1csTUFBTTtRQUFDO1FBQy9DVixnQkFBZ0JvQjtJQUNsQjtJQUVBLHFCQUFxQjtJQUNyQixNQUFNQyxtQkFBbUIsQ0FBQ3BCO1FBQ3hCLE1BQU1tQixZQUFZO1lBQUUsR0FBR3JCLFlBQVk7WUFBRUU7UUFBSztRQUMxQ0QsZ0JBQWdCb0I7SUFDbEI7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTUUsa0JBQWtCLE9BQU9DO1FBQzdCLElBQUksQ0FBQ3BDLGlCQUFpQjtZQUNwQnFDLE1BQU07WUFDTjtRQUNGO1FBRUEsSUFBSTtZQUNGLE1BQU1DLGNBQWMsTUFBTXhDLGtFQUFnQkEsQ0FBQ3lDLFlBQVksQ0FBQ0g7WUFDeERSLFFBQVFZLEdBQUcsQ0FBQyxtQkFBbUJGO1lBQy9CLDBDQUEwQztZQUMxQ25DLE9BQU9zQyxJQUFJLENBQUMsWUFBMkIsT0FBZkgsWUFBWUksRUFBRTtRQUN4QyxFQUFFLE9BQU9mLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUNVLE1BQU07UUFDUjtJQUNGO0lBRUEsSUFBSW5DLFdBQVc7UUFDYixxQkFDRSw4REFBQ1YsbUVBQWVBOzs4QkFDZCw4REFBQ1QsK0RBQVVBOzs7Ozs4QkFDWCw4REFBQ1EsZ0VBQVlBOzhCQUNYLDRFQUFDb0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS25DO0lBRUEscUJBQ0UsOERBQUNwRCxtRUFBZUE7OzBCQUNkLDhEQUFDVCwrREFBVUE7Ozs7OzBCQUNYLDhEQUFDUSxnRUFBWUE7O2tDQUNYLDhEQUFDc0Q7d0JBQU9ELFdBQVU7a0NBQ2hCLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNuRCxrRUFBY0E7b0NBQUNtRCxXQUFVOzs7Ozs7OENBQzFCLDhEQUFDdEQsK0RBQVNBO29DQUNSd0QsYUFBWTtvQ0FDWkYsV0FBVTs7Ozs7OzhDQUVaLDhEQUFDNUQsaUVBQVVBOzhDQUNULDRFQUFDRyxxRUFBY0E7OzBEQUNiLDhEQUFDRixxRUFBY0E7Z0RBQUMyRCxXQUFVOzBEQUN4Qiw0RUFBQzFELHFFQUFjQTtvREFBQzZELE1BQUs7OERBQWE7Ozs7Ozs7Ozs7OzBEQUlwQyw4REFBQzFELDBFQUFtQkE7Z0RBQUN1RCxXQUFVOzs7Ozs7MERBQy9CLDhEQUFDM0QscUVBQWNBOzBEQUNiLDRFQUFDRyxxRUFBY0E7OERBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNMUIsOERBQUN1RDt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEOztrREFDQyw4REFBQ0s7d0NBQUdKLFdBQVU7a0RBQ1g1QyxrQkFBa0IsaUJBQTRCLE9BQVhDLGlCQUFBQSwyQkFBQUEsS0FBTWdELElBQUksRUFBQyxPQUFLOzs7Ozs7a0RBRXRELDhEQUFDQzt3Q0FBRU4sV0FBVTtrREFDVjVDLGtCQUNHLGtGQUNBOzs7Ozs7Ozs7Ozs7MENBTVIsOERBQUNKLHlFQUFlQTtnQ0FDZHVELFVBQVVuQjtnQ0FDVjlCLFdBQVdJO2dDQUNYVSxlQUFlQTs7Ozs7OzRCQUloQlYsa0NBQ0MsOERBQUNxQztnQ0FBSUMsV0FBVTswQ0FDWlEsTUFBTUMsSUFBSSxDQUFDO29DQUFFQyxRQUFRO2dDQUFFLEdBQUdDLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxrQkFDakMsOERBQUNkO3dDQUFZQyxXQUFVOzswREFDckIsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7O3VDQUpQYTs7Ozs7Ozs7O3VDQVFackQsV0FBV2tELE1BQU0sR0FBRyxrQkFDdEIsOERBQUNYO2dDQUFJQyxXQUFVOzBDQUNaeEMsV0FBV21ELEdBQUcsQ0FBQyxDQUFDRywwQkFDZiw4REFBQy9ELHFFQUFhQTt3Q0FFWitELFdBQVdBO3dDQUNYQyxhQUFheEI7dUNBRlJ1QixVQUFVaEIsRUFBRTs7Ozs7Ozs7O3FEQU92Qiw4REFBQ0M7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDTTt3Q0FBRU4sV0FBVTtrREFBNkI7Ozs7OztrREFDMUMsOERBQUNNO3dDQUFFTixXQUFVO2tEQUFnQzs7Ozs7Ozs7Ozs7OzRCQUtoRCxDQUFDdEMscUJBQXFCRixXQUFXa0QsTUFBTSxHQUFHLG1CQUN6Qyw4REFBQ3pELCtEQUFVQTtnQ0FDVGEsYUFBYUE7Z0NBQ2JGLFlBQVlBO2dDQUNab0QsY0FBYzFCO2dDQUNkaEMsV0FBV0k7Ozs7Ozs0QkFLZCxDQUFDTixtQkFBbUJJLFdBQVdrRCxNQUFNLEdBQUcsbUJBQ3ZDLDhEQUFDWDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNpQjt3Q0FBR2pCLFdBQVU7a0RBQTZCOzs7Ozs7a0RBQzNDLDhEQUFDTTt3Q0FBRU4sV0FBVTtrREFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVN4RDtHQXZMd0I3Qzs7UUFDdUJMLDJEQUFPQTtRQUNyQ1osc0RBQVNBOzs7S0FGRmlCIiwic291cmNlcyI6WyJFOlxcYmVzc3RpZWt1XFxmcm9udGVuZFxcc3JjXFxhcHBcXGRhc2hib2FyZFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuaW1wb3J0IHsgQXBwU2lkZWJhciB9IGZyb20gXCJAL2NvbXBvbmVudHMvYXBwLXNpZGViYXJcIlxyXG5pbXBvcnQge1xyXG4gIEJyZWFkY3J1bWIsXHJcbiAgQnJlYWRjcnVtYkl0ZW0sXHJcbiAgQnJlYWRjcnVtYkxpbmssXHJcbiAgQnJlYWRjcnVtYkxpc3QsXHJcbiAgQnJlYWRjcnVtYlBhZ2UsXHJcbiAgQnJlYWRjcnVtYlNlcGFyYXRvcixcclxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2JyZWFkY3J1bWJcIlxyXG5pbXBvcnQgeyBTZXBhcmF0b3IgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlcGFyYXRvclwiXHJcbmltcG9ydCB7XHJcbiAgU2lkZWJhckluc2V0LFxyXG4gIFNpZGViYXJQcm92aWRlcixcclxuICBTaWRlYmFyVHJpZ2dlcixcclxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NpZGViYXJcIlxyXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkAvY29udGV4dHMvYXV0aC1jb250ZXh0XCJcclxuaW1wb3J0IHsgQ2hhcmFjdGVyQ2FyZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvY2hhcmFjdGVyLWNhcmRcIlxyXG5pbXBvcnQgeyBDaGFyYWN0ZXJTZWFyY2ggfSBmcm9tIFwiQC9jb21wb25lbnRzL2NoYXJhY3Rlci1zZWFyY2hcIlxyXG5pbXBvcnQgeyBQYWdpbmF0aW9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy9wYWdpbmF0aW9uXCJcclxuaW1wb3J0IHsgY2hhcmFjdGVyU2VydmljZSB9IGZyb20gXCJAL3NlcnZpY2VzL2NoYXJhY3RlclwiXHJcbmltcG9ydCB7IENoYXJhY3RlciwgR2V0Q2hhcmFjdGVyc1BhcmFtcyB9IGZyb20gXCJAL3R5cGVzL2NoYXJhY3RlclwiXHJcbmltcG9ydCB7IEF1dGhNb2RhbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvYXV0aC9hdXRoLW1vZGFsXCJcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XHJcbiAgY29uc3QgeyBpc0F1dGhlbnRpY2F0ZWQsIHVzZXIsIGlzTG9hZGluZyB9ID0gdXNlQXV0aCgpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IFtjaGFyYWN0ZXJzLCBzZXRDaGFyYWN0ZXJzXSA9IHVzZVN0YXRlPENoYXJhY3RlcltdPihbXSk7XHJcbiAgY29uc3QgW2NoYXJhY3RlcnNMb2FkaW5nLCBzZXRDaGFyYWN0ZXJzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbdG90YWxQYWdlcywgc2V0VG90YWxQYWdlc10gPSB1c2VTdGF0ZSgxKTtcclxuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDEpO1xyXG4gIGNvbnN0IFtzZWFyY2hQYXJhbXMsIHNldFNlYXJjaFBhcmFtc10gPSB1c2VTdGF0ZTxHZXRDaGFyYWN0ZXJzUGFyYW1zPih7IHBhZ2U6IDEsIGxpbWl0OiAxMiB9KTtcclxuICBjb25zdCBbYXZhaWxhYmxlVGFncywgc2V0QXZhaWxhYmxlVGFnc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pO1xyXG4gIGNvbnN0IFtzaG93QXV0aE1vZGFsLCBzZXRTaG93QXV0aE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbcGVuZGluZ0NoYXJhY3RlcklkLCBzZXRQZW5kaW5nQ2hhcmFjdGVySWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIC8vIEZldGNoIGNoYXJhY3RlcnNcclxuICBjb25zdCBmZXRjaENoYXJhY3RlcnMgPSBhc3luYyAocGFyYW1zOiBHZXRDaGFyYWN0ZXJzUGFyYW1zKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRDaGFyYWN0ZXJzTG9hZGluZyh0cnVlKTtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjaGFyYWN0ZXJTZXJ2aWNlLmdldENoYXJhY3RlcnMocGFyYW1zKTtcclxuICAgICAgc2V0Q2hhcmFjdGVycyhyZXNwb25zZS5kYXRhKTtcclxuICAgICAgc2V0VG90YWxQYWdlcyhyZXNwb25zZS50b3RhbFBhZ2VzKTtcclxuICAgICAgc2V0Q3VycmVudFBhZ2UocmVzcG9uc2UuY3VycmVudFBhZ2UpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIGNoYXJhY3RlcnM6JywgZXJyb3IpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0Q2hhcmFjdGVyc0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIExvYWQgYXZhaWxhYmxlIHRhZ3Mgb24gbW91bnRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgbG9hZFRhZ3MgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGNvbnN0IHRhZ3MgPSBhd2FpdCBjaGFyYWN0ZXJTZXJ2aWNlLmdldEFsbFRhZ3MoKTtcclxuICAgICAgc2V0QXZhaWxhYmxlVGFncyh0YWdzKTtcclxuICAgIH07XHJcbiAgICBsb2FkVGFncygpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gTG9hZCBjaGFyYWN0ZXJzIG9uIG1vdW50IGFuZCB3aGVuIHNlYXJjaCBwYXJhbXMgY2hhbmdlXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZldGNoQ2hhcmFjdGVycyhzZWFyY2hQYXJhbXMpO1xyXG4gIH0sIFtzZWFyY2hQYXJhbXNdKTtcclxuXHJcbiAgLy8gSGFuZGxlIHNlYXJjaFxyXG4gIGNvbnN0IGhhbmRsZVNlYXJjaCA9IChwYXJhbXM6IEdldENoYXJhY3RlcnNQYXJhbXMpID0+IHtcclxuICAgIGNvbnN0IG5ld1BhcmFtcyA9IHsgLi4uc2VhcmNoUGFyYW1zLCAuLi5wYXJhbXMgfTtcclxuICAgIHNldFNlYXJjaFBhcmFtcyhuZXdQYXJhbXMpO1xyXG4gIH07XHJcblxyXG4gIC8vIEhhbmRsZSBwYWdlIGNoYW5nZVxyXG4gIGNvbnN0IGhhbmRsZVBhZ2VDaGFuZ2UgPSAocGFnZTogbnVtYmVyKSA9PiB7XHJcbiAgICBjb25zdCBuZXdQYXJhbXMgPSB7IC4uLnNlYXJjaFBhcmFtcywgcGFnZSB9O1xyXG4gICAgc2V0U2VhcmNoUGFyYW1zKG5ld1BhcmFtcyk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIHN0YXJ0IGNoYXRcclxuICBjb25zdCBoYW5kbGVTdGFydENoYXQgPSBhc3luYyAoY2hhcmFjdGVySWQ6IHN0cmluZykgPT4ge1xyXG4gICAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcclxuICAgICAgYWxlcnQoJ1BsZWFzZSBzaWduIGluIHRvIHN0YXJ0IGNoYXR0aW5nJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBjaGF0U2Vzc2lvbiA9IGF3YWl0IGNoYXJhY3RlclNlcnZpY2UuaW5pdGlhdGVDaGF0KGNoYXJhY3RlcklkKTtcclxuICAgICAgY29uc29sZS5sb2coJ0NoYXQgaW5pdGlhdGVkOicsIGNoYXRTZXNzaW9uKTtcclxuICAgICAgLy8gTmF2aWdhdGUgdG8gY2hhdCBwYWdlIHdpdGggdGhlIG5ldyBjaGF0XHJcbiAgICAgIHJvdXRlci5wdXNoKGAvY2hhdD9pZD0ke2NoYXRTZXNzaW9uLmlkfWApO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGluaXRpYXRlIGNoYXQ6JywgZXJyb3IpO1xyXG4gICAgICBhbGVydCgnRmFpbGVkIHRvIHN0YXJ0IGNoYXQuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgaWYgKGlzTG9hZGluZykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPFNpZGViYXJQcm92aWRlcj5cclxuICAgICAgICA8QXBwU2lkZWJhciAvPlxyXG4gICAgICAgIDxTaWRlYmFySW5zZXQ+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtc2NyZWVuXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPkxvYWRpbmcuLi48L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvU2lkZWJhckluc2V0PlxyXG4gICAgICA8L1NpZGViYXJQcm92aWRlcj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPFNpZGViYXJQcm92aWRlcj5cclxuICAgICAgPEFwcFNpZGViYXIgLz5cclxuICAgICAgPFNpZGViYXJJbnNldD5cclxuICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImZsZXggaC0xNiBzaHJpbmstMCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtNFwiPlxyXG4gICAgICAgICAgICA8U2lkZWJhclRyaWdnZXIgY2xhc3NOYW1lPVwiLW1sLTFcIiAvPlxyXG4gICAgICAgICAgICA8U2VwYXJhdG9yXHJcbiAgICAgICAgICAgICAgb3JpZW50YXRpb249XCJ2ZXJ0aWNhbFwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXItMiBkYXRhLVtvcmllbnRhdGlvbj12ZXJ0aWNhbF06aC00XCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPEJyZWFkY3J1bWI+XHJcbiAgICAgICAgICAgICAgPEJyZWFkY3J1bWJMaXN0PlxyXG4gICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJJdGVtIGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiPlxyXG4gICAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYkxpbmsgaHJlZj1cIi9kYXNoYm9hcmRcIj5cclxuICAgICAgICAgICAgICAgICAgICBCZXN0aWVrdVxyXG4gICAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJMaW5rPlxyXG4gICAgICAgICAgICAgICAgPC9CcmVhZGNydW1iSXRlbT5cclxuICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iU2VwYXJhdG9yIGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiIC8+XHJcbiAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYkl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iUGFnZT5DaGFyYWN0ZXIgQ2hhdDwvQnJlYWRjcnVtYlBhZ2U+XHJcbiAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJJdGVtPlxyXG4gICAgICAgICAgICAgIDwvQnJlYWRjcnVtYkxpc3Q+XHJcbiAgICAgICAgICAgIDwvQnJlYWRjcnVtYj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvaGVhZGVyPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LTEgZmxleC1jb2wgZ2FwLTYgcC00IHB0LTBcIj5cclxuICAgICAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTJcIj5cclxuICAgICAgICAgICAgICB7aXNBdXRoZW50aWNhdGVkID8gYFdlbGNvbWUgYmFjaywgJHt1c2VyPy5uYW1lfSFgIDogJ1dlbGNvbWUgdG8gQmVzdGlla3UnfVxyXG4gICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgICAgICB7aXNBdXRoZW50aWNhdGVkXHJcbiAgICAgICAgICAgICAgICA/ICdDaG9vc2UgYSBjaGFyYWN0ZXIgdG8gc3RhcnQgY2hhdHRpbmcgd2l0aCBBSSBjb21wYW5pb25zIGluIGltbWVyc2l2ZSBzdG9yaWVzLidcclxuICAgICAgICAgICAgICAgIDogJ0V4cGxvcmUgb3VyIEFJIGNoYXJhY3RlcnMgYW5kIHNpZ24gaW4gdG8gc3RhcnQgY2hhdHRpbmcgaW4gaW1tZXJzaXZlIHN0b3JpZXMuJ1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIFNlYXJjaCBhbmQgRmlsdGVycyAqL31cclxuICAgICAgICAgIDxDaGFyYWN0ZXJTZWFyY2hcclxuICAgICAgICAgICAgb25TZWFyY2g9e2hhbmRsZVNlYXJjaH1cclxuICAgICAgICAgICAgaXNMb2FkaW5nPXtjaGFyYWN0ZXJzTG9hZGluZ31cclxuICAgICAgICAgICAgYXZhaWxhYmxlVGFncz17YXZhaWxhYmxlVGFnc31cclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgey8qIENoYXJhY3RlcnMgR3JpZCAqL31cclxuICAgICAgICAgIHtjaGFyYWN0ZXJzTG9hZGluZyA/IChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGF1dG8tcm93cy1taW4gZ2FwLTQgbWQ6Z3JpZC1jb2xzLTMgbGc6Z3JpZC1jb2xzLTRcIj5cclxuICAgICAgICAgICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogOCB9KS5tYXAoKF8sIGkpID0+IChcclxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJiZy1jYXJkIGJvcmRlciByb3VuZGVkLXhsIHAtNCBhbmltYXRlLXB1bHNlXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctbXV0ZWQvNTAgYXNwZWN0LXNxdWFyZSByb3VuZGVkLWxnIG1iLTNcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1tdXRlZC81MCByb3VuZGVkIG1iLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1tdXRlZC81MCByb3VuZGVkIG1iLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1tdXRlZC81MCByb3VuZGVkIHctMi8zXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICkgOiBjaGFyYWN0ZXJzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBhdXRvLXJvd3MtbWluIGdhcC00IG1kOmdyaWQtY29scy0zIGxnOmdyaWQtY29scy00XCI+XHJcbiAgICAgICAgICAgICAge2NoYXJhY3RlcnMubWFwKChjaGFyYWN0ZXIpID0+IChcclxuICAgICAgICAgICAgICAgIDxDaGFyYWN0ZXJDYXJkXHJcbiAgICAgICAgICAgICAgICAgIGtleT17Y2hhcmFjdGVyLmlkfVxyXG4gICAgICAgICAgICAgICAgICBjaGFyYWN0ZXI9e2NoYXJhY3Rlcn1cclxuICAgICAgICAgICAgICAgICAgb25TdGFydENoYXQ9e2hhbmRsZVN0YXJ0Q2hhdH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtYi00XCI+Tm8gY2hhcmFjdGVycyBmb3VuZCBtYXRjaGluZyB5b3VyIGNyaXRlcmlhLjwvcD5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlRyeSBhZGp1c3RpbmcgeW91ciBzZWFyY2ggb3IgZmlsdGVycy48L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICB7LyogUGFnaW5hdGlvbiAqL31cclxuICAgICAgICAgIHshY2hhcmFjdGVyc0xvYWRpbmcgJiYgY2hhcmFjdGVycy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgPFBhZ2luYXRpb25cclxuICAgICAgICAgICAgICBjdXJyZW50UGFnZT17Y3VycmVudFBhZ2V9XHJcbiAgICAgICAgICAgICAgdG90YWxQYWdlcz17dG90YWxQYWdlc31cclxuICAgICAgICAgICAgICBvblBhZ2VDaGFuZ2U9e2hhbmRsZVBhZ2VDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgaXNMb2FkaW5nPXtjaGFyYWN0ZXJzTG9hZGluZ31cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgey8qIFNpZ24gaW4gQ1RBIGZvciBub24tYXV0aGVudGljYXRlZCB1c2VycyAqL31cclxuICAgICAgICAgIHshaXNBdXRoZW50aWNhdGVkICYmIGNoYXJhY3RlcnMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCBwLTYgYmctbXV0ZWQvMzAgcm91bmRlZC14bCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItMlwiPlJlYWR5IHRvIHN0YXJ0IGNoYXR0aW5nPzwvaDI+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgIFNpZ24gaW4gdG8gdW5sb2NrIHBlcnNvbmFsaXplZCBjb252ZXJzYXRpb25zIGFuZCBzYXZlIHlvdXIgY2hhdCBoaXN0b3J5LlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L1NpZGViYXJJbnNldD5cclxuICAgIDwvU2lkZWJhclByb3ZpZGVyPlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VSb3V0ZXIiLCJBcHBTaWRlYmFyIiwiQnJlYWRjcnVtYiIsIkJyZWFkY3J1bWJJdGVtIiwiQnJlYWRjcnVtYkxpbmsiLCJCcmVhZGNydW1iTGlzdCIsIkJyZWFkY3J1bWJQYWdlIiwiQnJlYWRjcnVtYlNlcGFyYXRvciIsIlNlcGFyYXRvciIsIlNpZGViYXJJbnNldCIsIlNpZGViYXJQcm92aWRlciIsIlNpZGViYXJUcmlnZ2VyIiwidXNlQXV0aCIsIkNoYXJhY3RlckNhcmQiLCJDaGFyYWN0ZXJTZWFyY2giLCJQYWdpbmF0aW9uIiwiY2hhcmFjdGVyU2VydmljZSIsIlBhZ2UiLCJpc0F1dGhlbnRpY2F0ZWQiLCJ1c2VyIiwiaXNMb2FkaW5nIiwicm91dGVyIiwiY2hhcmFjdGVycyIsInNldENoYXJhY3RlcnMiLCJjaGFyYWN0ZXJzTG9hZGluZyIsInNldENoYXJhY3RlcnNMb2FkaW5nIiwidG90YWxQYWdlcyIsInNldFRvdGFsUGFnZXMiLCJjdXJyZW50UGFnZSIsInNldEN1cnJlbnRQYWdlIiwic2VhcmNoUGFyYW1zIiwic2V0U2VhcmNoUGFyYW1zIiwicGFnZSIsImxpbWl0IiwiYXZhaWxhYmxlVGFncyIsInNldEF2YWlsYWJsZVRhZ3MiLCJzaG93QXV0aE1vZGFsIiwic2V0U2hvd0F1dGhNb2RhbCIsInBlbmRpbmdDaGFyYWN0ZXJJZCIsInNldFBlbmRpbmdDaGFyYWN0ZXJJZCIsImZldGNoQ2hhcmFjdGVycyIsInBhcmFtcyIsInJlc3BvbnNlIiwiZ2V0Q2hhcmFjdGVycyIsImRhdGEiLCJlcnJvciIsImNvbnNvbGUiLCJsb2FkVGFncyIsInRhZ3MiLCJnZXRBbGxUYWdzIiwiaGFuZGxlU2VhcmNoIiwibmV3UGFyYW1zIiwiaGFuZGxlUGFnZUNoYW5nZSIsImhhbmRsZVN0YXJ0Q2hhdCIsImNoYXJhY3RlcklkIiwiYWxlcnQiLCJjaGF0U2Vzc2lvbiIsImluaXRpYXRlQ2hhdCIsImxvZyIsInB1c2giLCJpZCIsImRpdiIsImNsYXNzTmFtZSIsImhlYWRlciIsIm9yaWVudGF0aW9uIiwiaHJlZiIsImgxIiwibmFtZSIsInAiLCJvblNlYXJjaCIsIkFycmF5IiwiZnJvbSIsImxlbmd0aCIsIm1hcCIsIl8iLCJpIiwiY2hhcmFjdGVyIiwib25TdGFydENoYXQiLCJvblBhZ2VDaGFuZ2UiLCJoMiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});