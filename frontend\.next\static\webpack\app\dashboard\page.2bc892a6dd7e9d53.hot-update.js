"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_character_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/character-card */ \"(app-pages-browser)/./src/components/character-card.tsx\");\n/* harmony import */ var _components_character_search__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/character-search */ \"(app-pages-browser)/./src/components/character-search.tsx\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction Page() {\n    _s();\n    const { isAuthenticated, user, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [characters, setCharacters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [charactersLoading, setCharactersLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [searchParams, setSearchParams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 12\n    });\n    // Fetch characters\n    const fetchCharacters = async (params)=>{\n        try {\n            setCharactersLoading(true);\n            const response = await _services_character__WEBPACK_IMPORTED_MODULE_9__.characterService.getCharacters(params);\n            setCharacters(response.data);\n            setTotalPages(response.totalPages);\n            setCurrentPage(response.currentPage);\n        } catch (error) {\n            console.error('Failed to fetch characters:', error);\n        } finally{\n            setCharactersLoading(false);\n        }\n    };\n    // Load characters on mount and when search params change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            fetchCharacters(searchParams);\n        }\n    }[\"Page.useEffect\"], [\n        searchParams\n    ]);\n    // Handle search\n    const handleSearch = (params)=>{\n        const newParams = {\n            ...searchParams,\n            ...params\n        };\n        setSearchParams(newParams);\n    };\n    // Handle start chat\n    const handleStartChat = async (characterId)=>{\n        if (!isAuthenticated) {\n            alert('Please sign in to start chatting');\n            return;\n        }\n        try {\n            const chatSession = await _services_character__WEBPACK_IMPORTED_MODULE_9__.characterService.initiateChat(characterId);\n            console.log('Chat initiated:', chatSession);\n            // TODO: Navigate to chat page\n            alert(\"Chat initiated! Session ID: \".concat(chatSession.id));\n        } catch (error) {\n            console.error('Failed to initiate chat:', error);\n            alert('Failed to start chat. Please try again.');\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_2__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-screen\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg\",\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_2__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_4__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbPage, {\n                                                    children: \"Character Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col gap-6 p-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold mb-2\",\n                                        children: isAuthenticated ? \"Welcome back, \".concat(user === null || user === void 0 ? void 0 : user.name, \"!\") : 'Welcome to Bestieku'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: isAuthenticated ? 'Choose a character to start chatting with AI companions in immersive stories.' : 'Explore our AI characters and sign in to start chatting in immersive stories.'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_character_search__WEBPACK_IMPORTED_MODULE_8__.CharacterSearch, {\n                                onSearch: handleSearch,\n                                isLoading: charactersLoading\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            charactersLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border rounded-xl p-4 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted/50 aspect-square rounded-lg mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted/50 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted/50 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted/50 rounded w-2/3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this) : characters.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4\",\n                                children: characters.map((character)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_character_card__WEBPACK_IMPORTED_MODULE_7__.CharacterCard, {\n                                        character: character,\n                                        onStartChat: handleStartChat\n                                    }, character.id, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"No characters found matching your criteria.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Try adjusting your search or filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            !isAuthenticated && characters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 p-6 bg-muted/30 rounded-xl text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Ready to start chatting?\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"Sign in to unlock personalized conversations and save your chat history.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(Page, \"s5qI1tDO653YDstvFHBp2sfmLIE=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});