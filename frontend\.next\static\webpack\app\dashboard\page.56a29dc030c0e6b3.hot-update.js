"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/castle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sword.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _components_nav_user__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/nav-user */ \"(app-pages-browser)/./src/components/nav-user.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \n\n\n\n\nconst data = {\n    navMain: [\n        {\n            title: \"Discover\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            isActive: true,\n            description: \"Explore AI characters\"\n        },\n        {\n            title: \"My Chats\",\n            url: \"/chat\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"Continue conversations\"\n        },\n        {\n            title: \"Favorites\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Saved characters\"\n        },\n        {\n            title: \"Settings\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Account & preferences\"\n        }\n    ],\n    quickActions: [\n        {\n            name: \"Fantasy\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"from-purple-500 to-pink-500\"\n        },\n        {\n            name: \"Romance\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"from-pink-500 to-rose-500\"\n        },\n        {\n            name: \"Adventure\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"from-orange-500 to-red-500\"\n        },\n        {\n            name: \"Sci-Fi\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"from-blue-500 to-cyan-500\"\n        }\n    ]\n};\nfunction AppSidebar(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n        variant: \"inset\",\n        ...props,\n        className: \"border-r-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarHeader, {\n                className: \"border-b-0 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                            size: \"lg\",\n                            asChild: true,\n                            className: \"hover:bg-transparent p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/dashboard\",\n                                className: \"group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-[#2DD4BF] to-[#14B8A6] text-white flex aspect-square size-12 items-center justify-center rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"size-6\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid flex-1 text-left leading-tight ml-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg bg-gradient-to-r from-[#2DD4BF] to-[#14B8A6] bg-clip-text text-transparent\",\n                                                children: \"Bestieku\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-muted-foreground font-medium\",\n                                                children: \"AI Character Chat\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarContent, {\n                className: \"px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3 mb-3\",\n                                children: \"Navigation\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            data.navMain.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: item.url,\n                                        className: \"flex items-center gap-3 px-3 h-12 rounded-xl transition-all duration-200 hover:bg-gradient-to-r hover:from-[#2DD4BF]/10 hover:to-[#14B8A6]/10 hover:scale-[1.02] group \".concat(item.isActive ? 'bg-gradient-to-r from-[#2DD4BF]/20 to-[#14B8A6]/20 border border-[#2DD4BF]/30' : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg \".concat(item.isActive ? 'bg-[#2DD4BF] text-white shadow-md' : 'bg-muted/50 text-muted-foreground group-hover:bg-[#2DD4BF]/20 group-hover:text-[#2DD4BF]', \" transition-all duration-200\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"size-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(item.isActive ? 'text-[#2DD4BF]' : ''),\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                }, item.title, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3\",\n                                children: \"Quick Categories\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: data.quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: action.url,\n                                        className: \"p-3 rounded-xl bg-gradient-to-br \".concat(action.color, \" text-white hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg group flex flex-col items-center\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                className: \"w-5 h-5 mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-medium\",\n                                                children: action.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, action.name, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarFooter, {\n                className: \"p-4 border-t-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_user__WEBPACK_IMPORTED_MODULE_2__.NavUser, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});